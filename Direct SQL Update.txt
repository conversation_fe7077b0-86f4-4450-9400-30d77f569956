-- METHOD 1: Direct SQL update (for testing/admin use)

-- First, find the user ID and package ID
SELECT 
  au.id as user_id,
  au.email,
  up.package_id,
  p.name as current_package
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
LEFT JOIN public.packages p ON up.package_id = p.id
WHERE au.email = '<EMAIL>'; -- Replace with actual email

-- Get available packages
SELECT id, name, slug FROM public.packages WHERE is_active = TRUE;

-- Update user to Professional package (package_id = 2)
UPDATE public.user_profiles 
SET 
  package_id = 2, -- Professional package
  subscription_status = 'active',
  subscription_start_date = NOW(),
  subscription_end_date = NOW() + INTERVAL '1 month', -- 1 month subscription
  updated_at = NOW()
WHERE email = '<EMAIL>'; -- Replace with actual email

-- OR update by user ID if you know it
UPDATE public.user_profiles 
SET 
  package_id = 3, -- Enterprise package
  subscription_status = 'active',
  subscription_start_date = NOW(),
  subscription_end_date = NULL, -- NULL = no expiration
  updated_at = NOW()
WHERE id = 'user-uuid-here'; -- Replace with actual user UUID

-- METHOD 2: Using the upgrade function (recommended)
-- First create the upgrade function:

CREATE OR REPLACE FUNCTION public.upgrade_user_package(
  target_user_email TEXT, 
  new_package_slug TEXT,
  subscription_duration_months INTEGER DEFAULT 1
)
RETURNS JSON AS $$
DECLARE
  package_id_var INTEGER;
  user_id_var UUID;
  result JSON;
BEGIN
  -- Get package ID from slug
  SELECT id INTO package_id_var 
  FROM public.packages 
  WHERE slug = new_package_slug AND is_active = TRUE;
  
  IF package_id_var IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Package not found: ' || new_package_slug
    );
  END IF;
  
  -- Get user ID from email
  SELECT au.id INTO user_id_var
  FROM auth.users au
  WHERE au.email = target_user_email;
  
  IF user_id_var IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'User not found: ' || target_user_email
    );
  END IF;
  
  -- Update or insert user profile
  INSERT INTO public.user_profiles (
    id, email, package_id, subscription_status, 
    subscription_start_date, subscription_end_date, updated_at
  )
  VALUES (
    user_id_var, target_user_email, package_id_var, 'active',
    NOW(), 
    CASE 
      WHEN subscription_duration_months > 0 THEN NOW() + (subscription_duration_months || ' months')::INTERVAL
      ELSE NULL 
    END,
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    package_id = package_id_var,
    subscription_status = 'active',
    subscription_start_date = NOW(),
    subscription_end_date = CASE 
      WHEN subscription_duration_months > 0 THEN NOW() + (subscription_duration_months || ' months')::INTERVAL
      ELSE NULL 
    END,
    updated_at = NOW();
  
  -- Return success with package info
  SELECT json_build_object(
    'success', TRUE,
    'user_id', user_id_var,
    'email', target_user_email,
    'package_name', p.name,
    'package_slug', p.slug,
    'subscription_duration_months', subscription_duration_months
  ) INTO result
  FROM public.packages p
  WHERE p.id = package_id_var;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Now use the function to upgrade users:

-- Upgrade user to Professional for 1 month
SELECT public.upgrade_user_package('<EMAIL>', 'professional', 1);

-- Upgrade user to Enterprise with no expiration (lifetime)
SELECT public.upgrade_user_package('<EMAIL>', 'enterprise', 0);

-- Upgrade user to Free (downgrade)
SELECT public.upgrade_user_package('<EMAIL>', 'free', 12);
