{"name": "Gateway Workflow", "nodes": [{"parameters": {"path": "gateway", "methods": ["POST"]}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 200]}, {"parameters": {"authentication": "supabase", "resource": "auth", "operation": "getUser", "options": {}}, "name": "Verify JWT", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [400, 200], "credentials": {"supabaseApi": "Supabase Account"}}, {"parameters": {"resource": "database", "operation": "select", "schema": "public", "table": "user_profiles", "filters": [{"column": "user_id", "value": "={{$json.id}}"}]}, "name": "Get User Profile", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [600, 200], "credentials": {"supabaseApi": "Supabase Account"}}, {"parameters": {"resource": "database", "operation": "select", "schema": "public", "table": "packages", "filters": [{"column": "id", "value": "={{$json[\"package_id\"]}}"}]}, "name": "Get Package Limits", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [800, 200], "credentials": {"supabaseApi": "Supabase Account"}}, {"parameters": {"resource": "database", "operation": "select", "schema": "public", "table": "usage", "filters": [{"column": "user_id", "value": "={{$json[\"id\"]}}"}, {"column": "date", "value": "={{$today}}"}]}, "name": "Check Usage Counters", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1000, 200], "credentials": {"supabaseApi": "Supabase Account"}}, {"parameters": {"functionCode": "const package = items[0].json;\nconst usage = items[1].json || { videos_downloaded: 0, channels_downloaded: 0 };\n\nreturn [{\n  json: {\n    userId: $json.user_id,\n    packageId: package.id,\n    package: {\n      name: package.name,\n      max_channels: package.max_channels,\n      max_videos_per_channel: package.max_videos_per_channel,\n      daily_video_limit: package.daily_video_limit,\n      wait_period_length: package.wait_period_length\n    },\n    usage,\n    allowed: usage.videos_downloaded < package.daily_video_limit\n  }\n}];"}, "name": "Set Global Variables", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1200, 200]}, {"parameters": {"conditions": {"boolean": [], "number": [{"value1": "={{$json.allowed}}", "operation": "isEqual", "value2": true}]}}, "name": "Check <PERSON><PERSON><PERSON> OK?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1400, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/listChannels", "method": "POST", "jsonParameters": true, "options": {}, "bodyParametersJson": "={{$json}}"}, "name": "Call Worker - List Channels", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1600, 100]}, {"parameters": {"url": "http://localhost:5678/webhook/listVideos", "method": "POST", "jsonParameters": true, "options": {}, "bodyParametersJson": "={{$json}}"}, "name": "Call Worker - List Videos", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1600, 200]}, {"parameters": {"url": "http://localhost:5678/webhook/downloadVideo", "method": "POST", "jsonParameters": true, "options": {}, "bodyParametersJson": "={{$json}}"}, "name": "Call Worker - Download Video", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1600, 300]}, {"parameters": {"statusCode": 200, "options": {}, "responseBody": "={{$json}}"}, "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1800, 200]}], "connections": {"Webhook Trigger": {"main": [[{"node": "Verify JWT", "type": "main", "index": 0}]]}, "Verify JWT": {"main": [[{"node": "Get User Profile", "type": "main", "index": 0}]]}, "Get User Profile": {"main": [[{"node": "Get Package Limits", "type": "main", "index": 0}]]}, "Get Package Limits": {"main": [[{"node": "Check Usage Counters", "type": "main", "index": 0}]]}, "Check Usage Counters": {"main": [[{"node": "Set Global Variables", "type": "main", "index": 0}]]}, "Set Global Variables": {"main": [[{"node": "Check <PERSON><PERSON><PERSON> OK?", "type": "main", "index": 0}]]}, "Check Quota OK?": {"main": [[{"node": "Call Worker - List Channels", "type": "main", "index": 0}, {"node": "Call Worker - List Videos", "type": "main", "index": 0}, {"node": "Call Worker - Download Video", "type": "main", "index": 0}]]}, "Call Worker - List Channels": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Call Worker - List Videos": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}, "Call Worker - Download Video": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}}