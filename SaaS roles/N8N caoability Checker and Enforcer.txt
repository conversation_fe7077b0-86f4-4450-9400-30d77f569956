// N8N Code Node for SaaS Capability Checking and Enforcement

const SUPABASE_URL = "https://db.homelynx.qzz.io";
const SUPABASE_SERVICE_KEY = "eyJ..."; // Your service role key

// Function to get user capabilities from database
async function getUserCapabilities(userId) {
  const response = await fetch(`${SUPABASE_URL}/rpc/get_user_capabilities`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_SERVICE_KEY,
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ user_id: userId })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get capabilities: ${await response.text()}`);
  }
  
  return await response.json();
}

// Function to check if user can download video
function canDownloadVideo(capabilities, videoInfo = {}) {
  const limits = capabilities.limits_reached;
  const packageLimits = capabilities.package;
  
  // Check subscription status
  if (capabilities.usage.subscription_status !== 'active') {
    return {
      allowed: false,
      reason: 'subscription_inactive',
      message: 'Your subscription is not active'
    };
  }
  
  // Check daily limit
  if (limits.daily_limit) {
    return {
      allowed: false,
      reason: 'daily_limit_reached',
      message: `Daily download limit of ${packageLimits.video_downloads_per_day} reached`
    };
  }
  
  // Check monthly limit
  if (limits.monthly_limit) {
    return {
      allowed: false,
      reason: 'monthly_limit_reached',
      message: `Monthly download limit of ${packageLimits.video_downloads_per_month} reached`
    };
  }
  
  // Check storage limit
  if (limits.storage_limit) {
    return {
      allowed: false,
      reason: 'storage_limit_reached',
      message: `Storage limit of ${packageLimits.storage_limit_gb}GB reached`
    };
  }
  
  // Check video-specific limits if provided
  if (videoInfo.duration_minutes && packageLimits.max_video_duration_minutes > 0) {
    if (videoInfo.duration_minutes > packageLimits.max_video_duration_minutes) {
      return {
        allowed: false,
        reason: 'video_too_long',
        message: `Video duration (${videoInfo.duration_minutes}min) exceeds limit of ${packageLimits.max_video_duration_minutes}min`
      };
    }
  }
  
  if (videoInfo.size_mb && packageLimits.max_video_size_mb > 0) {
    if (videoInfo.size_mb > packageLimits.max_video_size_mb) {
      return {
        allowed: false,
        reason: 'video_too_large',
        message: `Video size (${videoInfo.size_mb}MB) exceeds limit of ${packageLimits.max_video_size_mb}MB`
      };
    }
  }
  
  // Check quality restrictions
  const qualityOrder = { 'SD': 1, 'HD': 2, '4K': 3 };
  if (videoInfo.requested_quality && packageLimits.max_video_quality) {
    const maxQualityLevel = qualityOrder[packageLimits.max_video_quality] || 1;
    const requestedQualityLevel = qualityOrder[videoInfo.requested_quality] || 1;
    
    if (requestedQualityLevel > maxQualityLevel) {
      return {
        allowed: false,
        reason: 'quality_too_high',
        message: `Requested quality (${videoInfo.requested_quality}) exceeds your plan limit (${packageLimits.max_video_quality})`
      };
    }
  }
  
  return {
    allowed: true,
    capabilities: capabilities.package,
    usage: capabilities.usage
  };
}

// Function to increment usage counters
async function incrementUsage(userId, videoSize = 0) {
  const response = await fetch(`${SUPABASE_URL}/rest/v1/user_profiles?id=eq.${userId}`, {
    method: 'PATCH',
    headers: {
      'apikey': SUPABASE_SERVICE_KEY,
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      videos_downloaded_today: 'videos_downloaded_today + 1',
      videos_downloaded_this_month: 'videos_downloaded_this_month + 1',
      total_storage_used_mb: `total_storage_used_mb + ${videoSize}`,
      last_download_date: new Date().toISOString().split('T')[0],
      updated_at: new Date().toISOString()
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to update usage: ${await response.text()}`);
  }
  
  return await response.json();
}

// Main execution logic
const operation = items[0].json.operation || 'check'; // 'check', 'enforce', 'increment'

try {
  // Extract user ID from JWT token
  const authHeader = items[0].json.headers?.authorization || items[0].json.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('MISSING_OR_INVALID_TOKEN');
  }
  
  const token = authHeader.replace('Bearer ', '');
  const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64url').toString());
  const userId = payload.sub;
  
  switch (operation) {
    case 'check':
      // Just check capabilities without enforcing
      const capabilities = await getUserCapabilities(userId);
      return [{
        json: {
          success: true,
          operation: 'check',
          user_id: userId,
          capabilities: capabilities,
          package_name: capabilities.package.name
        }
      }];
      
    case 'enforce':
      // Check if download is allowed with video info
      const videoInfo = {
        duration_minutes: items[0].json.video_duration_minutes,
        size_mb: items[0].json.video_size_mb,
        requested_quality: items[0].json.requested_quality || 'HD'
      };
      
      const userCaps = await getUserCapabilities(userId);
      const canDownload = canDownloadVideo(userCaps, videoInfo);
      
      return [{
        json: {
          success: canDownload.allowed,
          operation: 'enforce',
          user_id: userId,
          download_allowed: canDownload.allowed,
          reason: canDownload.reason || null,
          message: canDownload.message || 'Download approved',
          capabilities: canDownload.capabilities || null,
          usage: canDownload.usage || null,
          video_info: videoInfo
        }
      }];
      
    case 'increment':
      // Increment usage after successful download
      const videoSizeMB = items[0].json.video_size_mb || 0;
      const incrementResult = await incrementUsage(userId, videoSizeMB);
      
      return [{
        json: {
          success: true,
          operation: 'increment',
          user_id: userId,
          message: 'Usage updated successfully',
          video_size_mb: videoSizeMB
        }
      }];
      
    case 'reset_daily':
      // Reset daily counters (run this as a scheduled job)
      const resetResponse = await fetch(`${SUPABASE_URL}/rpc/reset_daily_counters`, {
        method: 'POST',
        headers: {
          'apikey': SUPABASE_SERVICE_KEY,
          'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!resetResponse.ok) {
        throw new Error('Failed to reset daily counters');
      }
      
      return [{
        json: {
          success: true,
          operation: 'reset_daily',
          message: 'Daily counters reset successfully'
        }
      }];
      
    default:
      throw new Error('Invalid operation. Use: check, enforce, increment, or reset_daily');
  }
  
} catch (error) {
  return [{
    json: {
      success: false,
      error: error.message,
      operation: operation,
      timestamp: new Date().toISOString()
    }
  }];
}
