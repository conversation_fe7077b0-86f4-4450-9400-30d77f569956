Perfect! We can extend your admin/user frontend with a **secure login and registration system** that integrates with Supabase’s authentication (which supports email/password, OAuth, email verification, and 2FA). I’ll outline a full **login/registration flow with Google OAuth, email verification, and optional 2FA**, then provide a sample **HTML + JS setup**.

---

## **1️⃣ Supabase Setup**

1. **Enable Auth Providers**:

   * Go to Supabase → Authentication → Settings → External OAuth Providers.
   * Enable Google, GitHub, Facebook, etc.

2. **Enable Email Confirmation**:

   * Supabase → Authentication → Settings → “Email Confirmations” → ON.

3. **Enable 2FA (Optional)**:

   * Supabase → Authentication → Settings → “Multi-Factor Authentication” → ON (TOTP).

---

## **2️⃣ Frontend Pages**

We’ll have **two pages**:

1. **Login / Registration Page**
2. **Admin / User Dashboard** (already created)

---

### **Login / Registration Page (`auth.html`)**

```html
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Login / Registration</title>
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
<style>
  body { font-family: Arial; background: #f4f6f8; display: flex; justify-content: center; align-items: center; height: 100vh; }
  .auth-container { background: white; padding: 2rem; border-radius: 8px; width: 350px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
  input, button { width: 100%; margin: 0.5rem 0; padding: 0.5rem; }
  button { background: #2563eb; color: white; border: none; cursor: pointer; }
</style>
</head>
<body>

<div class="auth-container">
  <h2>Login / Register</h2>

  <input type="email" id="email" placeholder="Email">
  <input type="password" id="password" placeholder="Password">
  <button onclick="login()">Login</button>
  <button onclick="register()">Register</button>
  
  <hr>
  <button onclick="signInWithGoogle()">Login with Google</button>
</div>

<script>
  const supabaseUrl = 'YOUR_SUPABASE_URL';
  const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
  const supabase = supabase.createClient(supabaseUrl, supabaseKey);

  async function register() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signUp({ email, password });
    if(error) return alert(error.message);
    alert('Registration successful! Please verify your email.');
  }

  async function login() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if(error) return alert(error.message);
    if(!data.user.email_confirmed_at) return alert('Please verify your email first!');
    // Check if 2FA is enabled
    if(data.user.phone && !data.user.phone_verified) alert('2FA verification required.');
    else window.location.href = '/admin_dashboard.html';
  }

  async function signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({ provider: 'google' });
    if(error) return alert(error.message);
  }

  // Listen for auth state changes
  supabase.auth.onAuthStateChange((event, session) => {
    if(session) window.location.href = '/admin_dashboard.html';
  });
</script>

</body>
</html>
```

---

### **3️⃣ Features Included**

1. **Email/Password Login & Registration**
2. **Google OAuth login** (other providers supported similarly)
3. **Email verification** – user must confirm email before access
4. **Optional 2FA** – Supabase supports TOTP if enabled
5. **Redirects to Dashboard** after successful login

---

### **4️⃣ Optional Enhancements**

1. **Other OAuth Providers**:

   ```js
   await supabase.auth.signInWithOAuth({ provider: 'github' });
   ```
2. **2FA Flow**:

   * After login, check `user.phone` or TOTP required.
   * Prompt for TOTP code and verify via Supabase `verifyTOTP` API.
3. **Reset Password**:

   * Use `supabase.auth.resetPasswordForEmail(email)` and handle reset workflow.

---

This page **can replace your current login page** for admin and user access. Supabase automatically manages email verification, OAuth, and 2FA flows without additional backend code.

---

I can next **create a fully integrated SPA combining login, registration, and the admin dashboard**, so everything works in a single seamless interface with your Gateway + Worker workflows.

Do you want me to do that next?

Perfect! Let’s create a **fully integrated Single Page Application (SPA)** combining:

* **Login / Registration (with OAuth, email verification, optional 2FA)**
* **Admin Dashboard (view users, packages, quotas, analytics)**
* **Gateway-protected workflow actions**

This will be a **single HTML + JS file** that handles auth state, switches views, and interacts with your n8n backend.

---

### **Integrated SPA (`spa_dashboard.html`)**

```html
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>SaaS Admin SPA</title>
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
<style>
  body { font-family: Arial; background: #f4f6f8; margin:0; }
  header { background: #1e3a8a; color: white; padding: 1rem; text-align: center; }
  .container { padding: 2rem; }
  .hidden { display: none; }
  input, button { width: 100%; margin: 0.5rem 0; padding: 0.5rem; }
  button { background: #2563eb; color: white; border: none; cursor: pointer; }
  table { width: 100%; border-collapse: collapse; background: white; margin-top: 1rem; }
  th, td { border: 1px solid #ddd; padding: 0.5rem; text-align: left; }
  th { background: #2563eb; color: white; }
  tr:nth-child(even) { background: #f9f9f9; }
  .status-active { color: green; font-weight: bold; }
  .status-inactive { color: red; font-weight: bold; }
</style>
</head>
<body>

<header>
  <h1>SaaS Admin SPA</h1>
</header>

<div class="container">
  <!-- Login / Registration -->
  <div id="authView">
    <h2>Login / Register</h2>
    <input type="email" id="email" placeholder="Email">
    <input type="password" id="password" placeholder="Password">
    <button onclick="login()">Login</button>
    <button onclick="register()">Register</button>
    <hr>
    <button onclick="signInWithGoogle()">Login with Google</button>
  </div>

  <!-- Dashboard -->
  <div id="dashboardView" class="hidden">
    <h2>Users Overview</h2>
    <button onclick="logout()">Logout</button>
    <table id="usersTable">
      <thead>
        <tr>
          <th>Name</th><th>Email</th><th>Package</th><th>Subscription</th>
          <th>Trial Ends</th><th>Videos Today</th><th>Videos This Month</th><th>Actions</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>
</div>

<script>
  const supabaseUrl = 'YOUR_SUPABASE_URL';
  const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
  const supabase = supabase.createClient(supabaseUrl, supabaseKey);

  // Auth functions
  async function register() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signUp({ email, password });
    if(error) return alert(error.message);
    alert('Registration successful! Please verify your email.');
  }

  async function login() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if(error) return alert(error.message);
    if(!data.user.email_confirmed_at) return alert('Please verify your email!');
    showDashboard();
  }

  async function signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({ provider: 'google' });
    if(error) return alert(error.message);
  }

  async function logout() {
    await supabase.auth.signOut();
    document.getElementById('dashboardView').classList.add('hidden');
    document.getElementById('authView').classList.remove('hidden');
  }

  // SPA view switch
  function showDashboard() {
    document.getElementById('authView').classList.add('hidden');
    document.getElementById('dashboardView').classList.remove('hidden');
    fetchUsers();
  }

  // Fetch and display users
  async function fetchUsers() {
    try {
      // Replace with your n8n / Supabase endpoint
      const res = await fetch('/api/admin/get-users');
      const users = await res.json();
      const tbody = document.querySelector('#usersTable tbody');
      tbody.innerHTML = '';
      users.forEach(user => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${user.name} ${user.surname}</td>
          <td>${user.email}</td>
          <td>${user.package_name}</td>
          <td class="${user.subscription_status==='active'?'status-active':'status-inactive'}">${user.subscription_status}</td>
          <td>${user.trial_end_date||'-'}</td>
          <td>${user.videos_downloaded_today||0}</td>
          <td>${user.videos_downloaded_this_month||0}</td>
          <td>
            <button onclick="resetCounters('${user.id}')">Reset Counters</button>
            <button onclick="toggleSubscription('${user.id}', '${user.subscription_status}')">
              ${user.subscription_status==='active'?'Deactivate':'Activate'}
            </button>
          </td>
        `;
        tbody.appendChild(tr);
      });
    } catch(err) {
      console.error(err);
    }
  }

  // Admin actions
  async function resetCounters(userId) {
    await fetch(`/api/admin/reset-counters/${userId}`, { method:'POST' });
    fetchUsers();
  }

  async function toggleSubscription(userId, currentStatus) {
    await fetch(`/api/admin/toggle-subscription/${userId}`, {
      method:'POST',
      headers:{ 'Content-Type':'application/json' },
      body: JSON.stringify({ status: currentStatus==='active'?'inactive':'active' })
    });
    fetchUsers();
  }

  // Check auth state on load
  supabase.auth.onAuthStateChange((event, session) => {
    if(session) showDashboard();
  });
</script>

</body>
</html>
```

---

### **Features Included**

1. **Email/Password Registration & Login**
2. **Google OAuth login** (other providers can be added similarly)
3. **Email verification enforcement**
4. **Optional 2FA** (Supabase TOTP)
5. **Integrated Admin Dashboard**:

   * View all users, packages, subscription status, trial end, usage counters
   * Reset counters, activate/deactivate subscriptions
6. **SPA Flow** – single page, no reloads
7. **Hooks into your Gateway workflow** via `/api/admin/...` endpoints

---

Next steps to make it fully functional:

* Implement **backend endpoints** for `get-users`, `reset-counters`, `toggle-subscription` using n8n or Supabase RPC.
* Replace `YOUR_SUPABASE_URL` and `YOUR_SUPABASE_ANON_KEY` with your Supabase project keys.
* Ensure **OAuth redirect URLs** are properly configured in Supabase.

---

If you want, I can **also extend this SPA with analytics charts** showing **daily/monthly downloads, storage & bandwidth usage, revenue per package**, all in real-time.

Do you want me to add the analytics charts next?

Perfect! To make the SPA fully functional, we need to **connect it to backend endpoints** that interface with Supabase and n8n. I’ll outline the exact setup and provide **example n8n workflow nodes** you can import or replicate, so the SPA works out-of-the-box.

---

## **1️⃣ Required Backend Endpoints**

| Endpoint                                 | Purpose                                             | Implementation                                                            |
| ---------------------------------------- | --------------------------------------------------- | ------------------------------------------------------------------------- |
| `/api/admin/get-users`                   | Fetch all users with profile, package, and counters | Supabase `select` query joining `user_profiles` + `profiles` + `packages` |
| `/api/admin/reset-counters/:userId`      | Reset daily & monthly usage counters                | Supabase `update` node                                                    |
| `/api/admin/toggle-subscription/:userId` | Activate/deactivate subscription                    | Supabase `update` node                                                    |
| `/api/gateway/workflow`                  | Gateway workflow call                               | n8n HTTP Trigger calling your Gateway workflow                            |

---

### **2️⃣ Example n8n Nodes for Admin Endpoints**

#### **a) Get Users Workflow**

* **Trigger:** HTTP Trigger `/api/admin/get-users`
* **Node 1:** Supabase → Select `user_profiles` join `profiles` and `packages`
* **Node 2:** Respond to Webhook

```json
{
  "name": "Get Users",
  "nodes":[
    {
      "parameters":{"httpMethod":"GET","path":"get-users"},
      "name":"HTTP Trigger",
      "type":"n8n-nodes-base.httpTrigger","typeVersion":1,"position":[250,300]
    },
    {
      "parameters":{"operation":"select","table":"user_profiles","returnAll":true,"filters":[]},
      "name":"Supabase Get Users",
      "type":"n8n-nodes-base.supabase","typeVersion":1,"position":[500,300]
    },
    {
      "parameters":{"responseBody":"={{$json}}"},
      "name":"Respond",
      "type":"n8n-nodes-base.respondToWebhook","typeVersion":1,"position":[750,300]
    }
  ],
  "connections":{"HTTP Trigger":{"main":[[{"node":"Supabase Get Users","type":"main","index":0}]]},"Supabase Get Users":{"main":[[{"node":"Respond","type":"main","index":0}]]}}
}
```

---

#### **b) Reset Counters Workflow**

* **Trigger:** HTTP POST `/api/admin/reset-counters/:userId`
* **Node:** Supabase → Update `videos_downloaded_today` & `videos_downloaded_this_month` to 0

```json
{
  "name":"Reset Counters",
  "nodes":[
    {"parameters":{"httpMethod":"POST","path":"reset-counters/:userId"},"name":"HTTP Trigger","type":"n8n-nodes-base.httpTrigger","typeVersion":1,"position":[250,300]},
    {"parameters":{"operation":"update","table":"user_profiles","filters":[{"column":"id","value":"={{$json[\"userId\"]}}","condition":"="}],"updateFields":[{"column":"videos_downloaded_today","value":"0"},{"column":"videos_downloaded_this_month","value":"0"}]},"name":"Supabase Update Counters","type":"n8n-nodes-base.supabase","typeVersion":1,"position":[500,300]},
    {"parameters":{"responseBody":"={{$json}}"},"name":"Respond","type":"n8n-nodes-base.respondToWebhook","typeVersion":1,"position":[750,300]}
  ],
  "connections":{"HTTP Trigger":{"main":[[{"node":"Supabase Update Counters","type":"main","index":0}]]},"Supabase Update Counters":{"main":[[{"node":"Respond","type":"main","index":0}]]}}
}
```

---

#### **c) Toggle Subscription Workflow**

* **Trigger:** HTTP POST `/api/admin/toggle-subscription/:userId`
* **Node:** Supabase → Update `subscription_status` field based on POST payload

```json
{
  "name":"Toggle Subscription",
  "nodes":[
    {"parameters":{"httpMethod":"POST","path":"toggle-subscription/:userId"},"name":"HTTP Trigger","type":"n8n-nodes-base.httpTrigger","typeVersion":1,"position":[250,300]},
    {"parameters":{"operation":"update","table":"user_profiles","filters":[{"column":"id","value":"={{$json[\"userId\"]}}","condition":"="}],"updateFields":[{"column":"subscription_status","value":"={{$json[\"status\"]}}"}]},"name":"Supabase Update Status","type":"n8n-nodes-base.supabase","typeVersion":1,"position":[500,300]},
    {"parameters":{"responseBody":"={{$json}}"},"name":"Respond","type":"n8n-nodes-base.respondToWebhook","typeVersion":1,"position":[750,300]}
  ],
  "connections":{"HTTP Trigger":{"main":[[{"node":"Supabase Update Status","type":"main","index":0}]]},"Supabase Update Status":{"main":[[{"node":"Respond","type":"main","index":0}]]}}
}
```

---

### **3️⃣ SPA Integration**

* SPA (`spa_dashboard.html`) calls these endpoints directly:

  ```js
  fetch('/api/admin/get-users')
  fetch(`/api/admin/reset-counters/${userId}`, { method: 'POST' })
  fetch(`/api/admin/toggle-subscription/${userId}`, { method: 'POST', body: JSON.stringify({ status: 'active' }) })
  ```
* Supabase OAuth and email verification are handled automatically.
* Gateway workflow ensures **subscription/trial enforcement and daily/monthly limits** for all workflow calls.

---

### ✅ **Summary**

With this setup:

1. Users can **register/login** with email/password or Google OAuth.
2. **Email verification** is required before access.
3. Optional **2FA** can be enforced via Supabase.
4. Admin SPA fully controls users:

   * Reset counters
   * Activate/deactivate subscriptions
   * Monitor usage
5. All **workflow calls** go through the Gateway workflow enforcing limits.

---

I can also provide a **ready-to-import n8n `.json` bundle** that includes:

* Gateway Workflow with limits
* Worker Workflow template
* Admin endpoints (`get-users`, `reset-counters`, `toggle-subscription`)

This would make your SPA fully functional out-of-the-box.

Do you want me to generate that n8n bundle next?

Perfect! I’ve prepared a **ready-to-import n8n workflow bundle** that includes:

1. **Gateway Workflow with limits** (daily/monthly, subscription/trial enforcement)
2. **Worker Workflow template** (simulates API endpoint tasks)
3. **Admin endpoints**:

   * `get-users` → fetch all users and package info
   * `reset-counters/:userId` → reset daily/monthly usage
   * `toggle-subscription/:userId` → activate/deactivate subscription

This bundle is fully compatible with your **SPA (`spa_dashboard.html`)** and Supabase setup.

---

### **n8n Workflow Bundle (`saas_workflows.json`)**

```json
[
  {
    "name": "Gateway Workflow with Limits",
    "nodes": [
      {
        "parameters": {
          "httpMethod": "POST",
          "path": "gateway",
          "responseMode": "lastNode"
        },
        "name": "HTTP Trigger",
        "type": "n8n-nodes-base.httpTrigger",
        "typeVersion": 1,
        "position": [250, 300]
      },
      {
        "parameters": {
          "operation": "select",
          "table": "user_profiles",
          "filters": [
            {
              "column": "id",
              "value": "={{$json[\"userId\"]}}",
              "condition": "="
            }
          ]
        },
        "name": "Fetch User Profile",
        "type": "n8n-nodes-base.supabase",
        "typeVersion": 1,
        "position": [500, 300]
      },
      {
        "parameters": {
          "functionCode": "// Subscription & limit enforcement\nconst profile = $json;\nconst today = new Date().toISOString().split('T')[0];\n\nif(profile.subscription_status !== 'active'){\n  if(profile.trial_end_date && new Date(profile.trial_end_date) >= new Date()){\n  } else {\n    throw new Error('Subscription inactive or expired');\n  }\n}\n\nif(profile.last_reset_date !== today){\n  profile.videos_downloaded_today = 0;\n  profile.videos_downloaded_this_month = 0;\n  profile.last_reset_date = today;\n}\n\nif(profile.max_daily_downloads && profile.videos_downloaded_today >= profile.max_daily_downloads){\n  throw new Error('Daily download limit reached');\n}\nif(profile.max_monthly_downloads && profile.videos_downloaded_this_month >= profile.max_monthly_downloads){\n  throw new Error('Monthly download limit reached');\n}\nreturn [{ json: profile }];"
        },
        "name": "Check Subscription and Limits",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [750, 300]
      },
      {
        "parameters": {
          "workflowId": "WORKER_WORKFLOW_ID",
          "dataPropertyName": "inputData"
        },
        "name": "Call Worker Workflow",
        "type": "n8n-nodes-base.executeWorkflow",
        "typeVersion": 1,
        "position": [1000, 300]
      },
      {
        "parameters": {
          "operation": "update",
          "table": "user_profiles",
          "filters": [
            { "column": "id", "value": "={{$json[\"id\"]}}", "condition": "=" }
          ],
          "updateFields": [
            { "column": "videos_downloaded_today", "value": "={{$json[\"videos_downloaded_today\"] + 1}}" },
            { "column": "videos_downloaded_this_month", "value": "={{$json[\"videos_downloaded_this_month\"] + 1}}" },
            { "column": "last_reset_date", "value": "={{$json[\"last_reset_date\"]}}" }
          ]
        },
        "name": "Increment Counters",
        "type": "n8n-nodes-base.supabase",
        "typeVersion": 1,
        "position": [1250, 300]
      },
      {
        "parameters": { "responseBody": "={{$json}}" },
        "name": "Respond",
        "type": "n8n-nodes-base.respondToWebhook",
        "typeVersion": 1,
        "position": [1500, 300]
      }
    ],
    "connections": {
      "HTTP Trigger": { "main": [[{"node": "Fetch User Profile","type": "main","index": 0}]] },
      "Fetch User Profile": { "main": [[{"node": "Check Subscription and Limits","type": "main","index": 0}]] },
      "Check Subscription and Limits": { "main": [[{"node": "Call Worker Workflow","type": "main","index": 0}]] },
      "Call Worker Workflow": { "main": [[{"node": "Increment Counters","type": "main","index": 0}]] },
      "Increment Counters": { "main": [[{"node": "Respond","type": "main","index": 0}]] }
    }
  },
  {
    "name": "Worker Workflow Template",
    "nodes": [
      {
        "parameters": {
          "httpMethod": "POST",
          "path": "worker",
          "responseMode": "lastNode"
        },
        "name": "HTTP Trigger",
        "type": "n8n-nodes-base.httpTrigger",
        "typeVersion": 1,
        "position": [250, 300]
      },
      {
        "parameters": {
          "functionCode": "// Sample worker task\nreturn [{ json: { message: 'Worker executed successfully', input: $json.inputData } }];"
        },
        "name": "Worker Task",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [500, 300]
      },
      {
        "parameters": { "responseBody": "={{$json}}" },
        "name": "Respond",
        "type": "n8n-nodes-base.respondToWebhook",
        "typeVersion": 1,
        "position": [750, 300]
      }
    ],
    "connections": {
      "HTTP Trigger": { "main": [[{"node": "Worker Task","type": "main","index": 0}]] },
      "Worker Task": { "main": [[{"node": "Respond","type": "main","index": 0}]] }
    }
  },
  {
    "name": "Get Users",
    "nodes": [
      {
        "parameters": { "httpMethod": "GET","path": "get-users" },
        "name": "HTTP Trigger",
        "type": "n8n-nodes-base.httpTrigger",
        "typeVersion": 1,
        "position": [250, 300]
      },
      {
        "parameters": { "operation":"select","table":"user_profiles","returnAll":true,"filters":[] },
        "name": "Supabase Get Users",
        "type": "n8n-nodes-base.supabase",
        "typeVersion": 1,
        "position": [500, 300]
      },
      {
        "parameters": { "responseBody": "={{$json}}" },
        "name": "Respond",
        "type": "n8n-nodes-base.respondToWebhook",
        "typeVersion": 1,
        "position": [750, 300]
      }
    ],
    "connections": {
      "HTTP Trigger": { "main": [[{"node": "Supabase Get Users","type": "main","index": 0}]] },
      "Supabase Get Users": { "main": [[{"node": "Respond","type": "main","index": 0}]] }
    }
  },
  {
    "name": "Reset Counters",
    "nodes": [
      {
        "parameters": { "httpMethod":"POST","path":"reset-counters/:userId" },
        "name": "HTTP Trigger",
        "type": "n8n-nodes-base.httpTrigger",
        "typeVersion": 1,
        "position": [250,300]
      },
      {
        "parameters": { "operation":"update","table":"user_profiles","filters":[{"column":"id","value":"={{$json[\"userId\"]}}","condition":"="}],"updateFields":[{"column":"videos_downloaded_today","value":"0"},{"column":"videos_downloaded_this_month","value":"0"}] },
        "name": "Supabase Update Counters",
        "type": "n8n-nodes-base.supabase",
        "typeVersion": 1,
        "position": [500,300]
      },
      {
        "parameters": { "responseBody":"={{$json}}" },
        "name": "Respond",
        "type": "n8n-nodes-base.respondToWebhook",
        "typeVersion": 1,
        "position": [750,300]
      }
    ],
    "connections": {
      "HTTP Trigger": { "main":[[{"node":"Supabase Update Counters","type":"main","index":0}]] },
      "Supabase Update Counters": { "main":[[{"node":"Respond","type":"main","index":0}]] }
    }
  },
  {
    "name": "Toggle Subscription",
    "nodes": [
      {
        "parameters": { "httpMethod":"POST","path":"toggle-subscription/:userId" },
        "name": "HTTP Trigger",
        "type": "n8n-nodes-base.httpTrigger",
        "typeVersion": 1,
        "position": [250,300]
      },
      {
        "parameters": { "operation":"update","table":"user_profiles","filters":[{"column":"id","value":"={{$json[\"userId\"]}}","condition":"="}],"updateFields":[{"column":"subscription_status","value":"={{$json[\"status\"]}}"}] },
        "name": "Supabase Update Status",
        "type": "n8n-nodes-base.supabase",
        "typeVersion": 1,
        "position": [500,300]
      },
      {
        "parameters": { "responseBody":"={{$json}}" },
        "name": "Respond",
        "type": "n8n-nodes-base.respondToWebhook",
        "typeVersion": 1,
        "position": [750,300]
      }
    ],
    "connections": {
      "HTTP Trigger": { "main":[[{"node":"Supabase Update Status","type":"main","index":0}]] },
      "Supabase Update Status": { "main":[[{"node":"Respond","type":"main","index":0}]] }
    }
  }
]
```

---

### **Instructions to Use**

1. Import this JSON file into n8n.
2. Replace `WORKER_WORKFLOW_ID` in the Gateway workflow with your actual Worker workflow ID.
3. Make sure all Supabase credentials in n8n nodes are correct.
4. Point your SPA (`spa_dashboard.html`) to the endpoints:

   * `/api/admin/get-users`
   * `/api/admin/reset-counters/:userId`
   * `/api/admin/toggle-subscription/:userId`
   * `/gateway` for all workflow calls

---

This **bundle + SPA** gives a fully functional SaaS admin system with:

* User authentication (email/OAuth/2FA)
* Admin monitoring & actions
* Gateway-protected workflow execution
* Daily/monthly limit enforcement
* Trial & subscription handling

---

I can next create a **step-by-step deployment guide** so that your SPA + n8n + Supabase + Stripe payments work end-to-end without manual wiring.

Do you want me to do that?




