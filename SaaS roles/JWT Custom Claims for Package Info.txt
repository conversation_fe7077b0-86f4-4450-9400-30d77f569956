-- Enhanced JWT custom claims to include package information
CREATE OR REPLACE FUNCTION auth.get_custom_claims(user_id UUID)
RETURNS JSONB
LANGUAGE SQL
STABLE
AS $$
  SELECT COALESCE(
    jsonb_build_object(
      'package_name', p.name,
      'package_slug', p.slug,
      'subscription_status', up.subscription_status,
      'video_downloads_per_day', p.video_downloads_per_day,
      'video_downloads_per_month', p.video_downloads_per_month,
      'max_video_quality', p.max_video_quality,
      'api_access', p.api_access,
      'priority_processing', p.priority_processing,
      'bulk_download', p.bulk_download,
      'concurrent_downloads', p.concurrent_downloads,
      'videos_downloaded_today', up.videos_downloaded_today,
      'videos_downloaded_this_month', up.videos_downloaded_this_month,
      'email_verified', auth.users.email_confirmed_at IS NOT NULL
    ),
    jsonb_build_object('package_name', 'free', 'subscription_status', 'active')
  )
  FROM auth.users
  LEFT JOIN public.user_profiles up ON auth.users.id = up.id
  LEFT JOIN public.packages p ON up.package_id = p.id
  WHERE auth.users.id = user_id;
$$;

-- Function to upgrade user package
CREATE OR REPLACE FUNCTION public.upgrade_user_package(
  target_user_id UUID, 
  new_package_slug TEXT,
  subscription_end_date TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  package_id_var INTEGER;
BEGIN
  -- Get package ID from slug
  SELECT id INTO package_id_var 
  FROM public.packages 
  WHERE slug = new_package_slug AND is_active = TRUE;
  
  IF package_id_var IS NULL THEN
    RAISE EXCEPTION 'Package not found: %', new_package_slug;
  END IF;
  
  -- Update user profile
  UPDATE public.user_profiles 
  SET 
    package_id = package_id_var,
    subscription_status = 'active',
    subscription_start_date = NOW(),
    subscription_end_date = subscription_end_date,
    updated_at = NOW()
  WHERE id = target_user_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check and enforce video download limits
CREATE OR REPLACE FUNCTION public.can_download_video(
  user_id UUID,
  video_duration_minutes INTEGER DEFAULT 0,
  video_size_mb INTEGER DEFAULT 0,
  requested_quality TEXT DEFAULT 'HD'
)
RETURNS JSON AS $$
DECLARE
  user_package RECORD;
  quality_levels JSON := '{"SD": 1, "HD": 2, "4K": 3}';
  max_quality_level INTEGER;
  requested_quality_level INTEGER;
BEGIN
  -- Get user package and usage info
  SELECT 
    up.*,
    p.name as package_name,
    p.video_downloads_per_day,
    p.video_downloads_per_month,
    p.max_video_duration_minutes,
    p.max_video_size_mb,
    p.max_video_quality,
    p.storage_limit_gb
  INTO user_package
  FROM public.user_profiles up
  JOIN public.packages p ON up.package_id = p.id
  WHERE up.id = user_id 
    AND up.subscription_status = 'active'
    AND (up.subscription_end_date IS NULL OR up.subscription_end_date > NOW());
  
  IF NOT FOUND THEN
    RETURN json_build_object(
      'allowed', FALSE,
      'reason', 'no_active_subscription',
      'message', 'No active subscription found'
    );
  END IF;
  
  -- Check daily limit
  IF user_package.video_downloads_per_day > 0 AND 
     user_package.videos_downloaded_today >= user_package.video_downloads_per_day THEN
    RETURN json_build_object(
      'allowed', FALSE,
      'reason', 'daily_limit_reached',
      'message', format('Daily limit of %s downloads reached', user_package.video_downloads_per_day)
    );
  END IF;
  
  -- Check monthly limit
  IF user_package.video_downloads_per_month > 0 AND 
     user_package.videos_downloaded_this_month >= user_package.video_downloads_per_month THEN
    RETURN json_build_object(
      'allowed', FALSE,
      'reason', 'monthly_limit_reached',
      'message', format('Monthly limit of %s downloads reached', user_package.video_downloads_per_month)
    );
  END IF;
  
  -- Check video duration
  IF user_package.max_video_duration_minutes > 0 AND 
     video_duration_minutes > user_package.max_video_duration_minutes THEN
    RETURN json_build_object(
      'allowed', FALSE,
      'reason', 'video_too_long',
      'message', format('Video duration (%s min) exceeds limit of %s min', 
                       video_duration_minutes, user_package.max_video_duration_minutes)
    );
  END IF;
  
  -- Check video size
  IF user_package.max_video_size_mb > 0 AND 
     video_size_mb > user_package.max_video_size_mb THEN
    RETURN json_build_object(
      'allowed', FALSE,
      'reason', 'video_too_large',
      'message', format('Video size (%s MB) exceeds limit of %s MB', 
                       video_size_mb, user_package.max_video_size_mb)
    );
  END IF;
  
  -- Check quality restriction
  max_quality_level := (quality_levels->>user_package.max_video_quality)::INTEGER;
  requested_quality_level := (quality_levels->>requested_quality)::INTEGER;
  
  IF requested_quality_level > max_quality_level THEN
    RETURN json_build_object(
      'allowed', FALSE,
      'reason', 'quality_too_high',
      'message', format('Requested quality (%s) exceeds plan limit (%s)', 
                       requested_quality, user_package.max_video_quality)
    );
  END IF;
  
  -- All checks passed
  RETURN json_build_object(
    'allowed', TRUE,
    'package_name', user_package.package_name,
    'remaining_daily', CASE 
      WHEN user_package.video_downloads_per_day = 0 THEN -1
      ELSE user_package.video_downloads_per_day - user_package.videos_downloaded_today
    END,
    'remaining_monthly', CASE 
      WHEN user_package.video_downloads_per_month = 0 THEN -1
      ELSE user_package.video_downloads_per_month - user_package.videos_downloaded_this_month
    END
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
