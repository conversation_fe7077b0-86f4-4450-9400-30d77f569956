The "Success. No rows returned" message is actually not an error - it's a success message from Supabase! This typically appears when you run SQL commands that don't return data (like CREATE TABLE, INSERT, etc.).

However, let me help you implement this step-by-step to avoid any potential issues:

## Step 1: Create Tables One by OneRun the above SQL first. After it completes successfully, run this:## Test Your Setup

After running all the steps above, test it with this query:## Common Issues and Solutions:

1. **"Success. No rows returned"** - This is normal for CREATE, INSERT, ALTER commands
2. **Permission denied** - Make sure you're running this as a database admin
3. **Table already exists** - I added `IF NOT EXISTS` to prevent errors
4. **Foreign key constraint fails** - Make sure you run the steps in order

## Quick Check Commands:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('packages', 'user_profiles');

-- Check if packages were inserted
SELECT COUNT(*) FROM public.packages;
```

Run each step one at a time and let me know which step (if any) gives you an actual error message. The "Success. No rows returned" message means the SQL executed successfully!



-- STEP 1: Create the packages table
-- Run this first, then check if it worked before proceeding

CREATE TABLE IF NOT EXISTS public.packages (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  video_downloads_per_day INTEGER DEFAULT 0,
  video_downloads_per_month INTEGER DEFAULT 0,
  max_video_duration_minutes INTEGER DEFAULT 0,
  max_video_size_mb INTEGER DEFAULT 0,
  max_video_quality TEXT DEFAULT 'SD',
  concurrent_downloads INTEGER DEFAULT 1,
  api_access BOOLEAN DEFAULT FALSE,
  priority_processing BOOLEAN DEFAULT FALSE,
  custom_watermark BOOLEAN DEFAULT FALSE,
  bulk_download BOOLEAN DEFAULT FALSE,
  storage_limit_gb INTEGER DEFAULT 0,
  bandwidth_limit_gb INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE
);


-- STEP 2: Insert the default packages
-- Only run this after Step 1 completes successfully

INSERT INTO public.packages (
  name, slug, description, price_monthly, price_yearly, 
  video_downloads_per_day, video_downloads_per_month, 
  max_video_duration_minutes, max_video_size_mb, 
  max_video_quality, concurrent_downloads, api_access, 
  priority_processing, bulk_download, storage_limit_gb
) VALUES
('Free', 'free', 'Basic video downloading', 0.00, 0.00, 5, 50, 10, 100, 'SD', 1, FALSE, FALSE, FALSE, 1),
('Professional', 'professional', 'Enhanced features for professionals', 19.99, 199.99, 50, 1000, 60, 500, 'HD', 3, TRUE, TRUE, TRUE, 10),
('Enterprise', 'enterprise', 'Unlimited access for teams', 99.99, 999.99, 0, 0, 0, 0, '4K', 10, TRUE, TRUE, TRUE, 0);



-- STEP 3: Create user profiles table
-- Only run this after Step 2 completes successfully

CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT,
  package_id INTEGER REFERENCES public.packages(id) DEFAULT 1,
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'expired', 'trial')),
  subscription_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  trial_end_date TIMESTAMP WITH TIME ZONE,
  videos_downloaded_today INTEGER DEFAULT 0,
  videos_downloaded_this_month INTEGER DEFAULT 0,
  last_download_date DATE,
  last_reset_date DATE DEFAULT CURRENT_DATE,
  total_storage_used_mb INTEGER DEFAULT 0,
  total_bandwidth_used_mb INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);


-- STEP 4: Enable Row Level Security and create policies
-- Only run this after Step 3 completes successfully

-- Enable RLS
ALTER TABLE public.packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Packages policies (anyone can view active packages)
CREATE POLICY "Anyone can view active packages" ON public.packages
  FOR SELECT USING (is_active = TRUE);

-- User profiles policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);


-- STEP 5: Create the main functions
-- Only run this after Step 4 completes successfully

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, package_id)
  VALUES (NEW.id, NEW.email, 1);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to get user capabilities
CREATE OR REPLACE FUNCTION public.get_user_capabilities(user_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'user_id', up.id,
    'package', json_build_object(
      'name', p.name,
      'slug', p.slug,
      'video_downloads_per_day', p.video_downloads_per_day,
      'video_downloads_per_month', p.video_downloads_per_month,
      'max_video_duration_minutes', p.max_video_duration_minutes,
      'max_video_size_mb', p.max_video_size_mb,
      'max_video_quality', p.max_video_quality,
      'concurrent_downloads', p.concurrent_downloads,
      'api_access', p.api_access,
      'priority_processing', p.priority_processing,
      'bulk_download', p.bulk_download,
      'storage_limit_gb', p.storage_limit_gb
    ),
    'usage', json_build_object(
      'videos_downloaded_today', up.videos_downloaded_today,
      'videos_downloaded_this_month', up.videos_downloaded_this_month,
      'total_storage_used_mb', up.total_storage_used_mb,
      'subscription_status', up.subscription_status
    ),
    'limits_reached', json_build_object(
      'daily_limit', CASE 
        WHEN p.video_downloads_per_day = 0 THEN FALSE
        ELSE up.videos_downloaded_today >= p.video_downloads_per_day
      END,
      'monthly_limit', CASE 
        WHEN p.video_downloads_per_month = 0 THEN FALSE
        ELSE up.videos_downloaded_this_month >= p.video_downloads_per_month
      END,
      'storage_limit', CASE 
        WHEN p.storage_limit_gb = 0 THEN FALSE
        ELSE (up.total_storage_used_mb / 1024.0) >= p.storage_limit_gb
      END
    )
  ) INTO result
  FROM public.user_profiles up
  JOIN public.packages p ON up.package_id = p.id
  WHERE up.id = user_id 
    AND up.subscription_status = 'active'
    AND (up.subscription_end_date IS NULL OR up.subscription_end_date > NOW());
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;


-- TEST: Verify everything is working
-- This should return your 3 packages

SELECT * FROM public.packages ORDER BY id;

-- Also check if any users have profiles
SELECT 
  up.id,
  up.email,
  p.name as package_name,
  up.subscription_status
FROM public.user_profiles up
JOIN public.packages p ON up.package_id = p.id
LIMIT 5;

























