To add roles to users in your self-hosted Supabase installation, you have several approaches. Here are the most common and effective methods:

## Method 1: Database Triggers (Recommended)

Create a database function and trigger that automatically adds roles when users are created:## Method 2: Custom JWT Claims Hook

For roles to appear directly in the JW<PERSON> token, you'll need to create a custom hook. Create this file in your Supabase project:## Method 3: N8N Workflow for Role Management

Here's an n8n workflow code node to manage user roles programmatically:## Configuration Steps:

### 1. **Set up the Database Schema**
Run the SQL from the first artifact in your Supabase SQL editor.

### 2. **Configure JWT Custom Claims (Optional)**
If you want roles in the JWT token itself, you'll need to configure a custom access token hook in your Supabase configuration.

### 3. **Use in N8N**
The second artifact provides three operations:
- **`get`**: Retrieve a user's role
- **`set`**: Assign a role to a user  
- **`verify`**: Verify JWT and get role information

### 4. **Example N8N Usage**

To assign a role:
```json
{
  "operation": "set",
  "user_id": "user-uuid-here",
  "role": "admin",
  "email": "<EMAIL>"
}
```

To verify JWT and get role:
```json
{
  "operation": "verify",
  "headers": {
    "authorization": "Bearer eyJ..."
  }
}
```

### 5. **Important Notes**

- Use your **service role key** (not anon key) for the role management operations
- The service role key has admin privileges and can bypass RLS
- Store sensitive keys in n8n environment variables
- The verification operation can work with tokens obtained from your curl command

This setup gives you a complete role-based access control system that integrates well with both Supabase auth and n8n workflows.


Yes, absolutely! What you're describing is a perfect use case for role-based access control, but instead of simple roles like "admin/user", you'll have subscription tiers with specific capability limits. Let me show you how to implement this:

## Database Schema for SaaS Subscription Tiers## N8N Backend Code for Capability Checking## JWT Custom Claims for Package Info## How This Works for Your SaaS:

### 1. **Package-Based Capabilities**
- **Free**: 5 videos/day, 50/month, 10min max, 100MB max, SD quality
- **Professional**: 50 videos/day, 1000/month, 60min max, 500MB max, HD quality, API access
- **Enterprise**: Unlimited downloads, no restrictions, 4K quality, priority processing

### 2. **N8N Workflow Integration**
Your video download workflow would:

```json
// Step 1: Check capabilities
{
  "operation": "enforce",
  "video_duration_minutes": 25,
  "video_size_mb": 150,
  "requested_quality": "HD"
}

// Step 2: If allowed, process download
// Step 3: Increment usage
{
  "operation": "increment",
  "video_size_mb": 150
}
```

### 3. **Frontend Integration**
The JWT token will contain package info, so your frontend can:
- Show remaining downloads
- Display package limits
- Suggest upgrades when limits are reached

### 4. **Usage Tracking**
- Automatic daily/monthly counter resets
- Storage usage tracking
- Real-time limit enforcement

### 5. **Upgrade Flow**
```sql
-- Upgrade user to Professional
SELECT public.upgrade_user_package(
  'user-uuid-here', 
  'professional', 
  NOW() + INTERVAL '1 month'
);
```

This system gives you complete control over user capabilities based on their subscription tier, with real-time enforcement in your n8n workflows.
