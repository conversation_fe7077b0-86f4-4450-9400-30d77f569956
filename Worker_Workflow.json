{"name": "Worker Example Workflow", "nodes": [{"parameters": {"path": "worker-endpoint", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Worker Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"workflowId": "Auth & Access Workflow"}, "id": "2", "name": "Run Auth Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\n\n// Example: enforce only PRO users\nif ($json.plan !== 'pro') {\n  return [{ status: 403, message: \"Upgrade to Pro to use this feature.\" }];\n}\n\nreturn [{ status: 200, message: \"Worker action completed\", user: $json.user }];"}, "id": "3", "name": "Check Access & Do Work", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}], "connections": {"Webhook (Worker Entry)": {"main": [[{"node": "Run Auth Workflow", "type": "main", "index": 0}]]}, "Run Auth Workflow": {"main": [[{"node": "Check Access & Do Work", "type": "main", "index": 0}]]}}, "active": false}