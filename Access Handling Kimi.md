Below is the pattern that scales best when:

- Your **public API is n8n webhook workflows**  
- You already use **Supabase Auth + Postgres (users, packages, roles)**  
- You want one place where **“can user X do Y?”** is answered

---

### 1. Split responsibilities clearly

| Layer | Concern | Lives |
|-------|---------|-------|
| **Frontend** | *Who* is the user (login, refresh, attach JWT) | React / Vue / Flutter |
| **Edge / Gateway** | *Is the request signed* and *what scopes does it carry* | n8n “Auth Gateway” workflow |
| **Domain workflows** | *Execute the business logic* (create project, delete file, …) | n8n “Worker” workflows |
| **Supabase** | Source of truth for users, packages, RBAC | Supabase Postgres + RLS |

---

### 2. Create ONE lightweight **“Auth Gateway”** workflow

- **Webhook URL** exposed to the internet (e.g. `/gate/<workflow-name>`).  
- Receives every frontend call **before** the real workflow.  
- Does in 1–2 nodes:

```typescript
// Node 1 – verify JWT
jwt.verify(token, SUPABASE_JWT_SECRET, { algorithms: ['HS256'] })

// Node 2 – fetch user + package row
const { data } = await supabase
  .from('user_packages')
  .select('package(name, max_projects, max_storage_mb)')
  .eq('user_id', jwt.sub)
  .single()

// Node 3 – enforce policy
if (action === 'create_project' && data.package.max_projects <= currentCount) {
  return respond(403, { error: 'quota_exceeded' })
}

// Node 4 – forward to the real workflow
$execution.resumeUrl = `https://n8n.internal/webhook/${workflowId}?userId=${jwt.sub}`
```

- **Returns 403 immediately** if rules fail.  
- **Returns 200 + signed internal JWT** (or just sets header) if rules pass.  
- Uses n8n **Respond to Webhook** node so the caller never sees the internal URL.

---

### 3. Keep domain workflows **dumb and single-purpose**

- Each workflow has **one job** (`createProject`, `deleteFile`, …).  
- They start with a **non-public** webhook (no auth) that is only reachable from the Auth Gateway (either same n8n instance on a private network, or by using n8n’s built-in “Execute Workflow” node).  
- They receive the already-decoded user-id and scopes in the payload, so they never hit Supabase for auth again.

---

### 4. Package / role mapping in Supabase

Table `packages`  
```
id | name    | max_projects | max_storage_mb | features (jsonb)
---+---------+--------------+----------------+----------------------------
1  | starter | 3            | 100            | { "webhooks": false }
2  | pro     | 25           | 1000           | { "webhooks": true }
```

Table `user_packages`  
```
user_id | package_id | expires_at
--------+------------+------------
```

Postgres views or RLS policies can enforce limits if you also expose Supabase REST directly, but the n8n Gateway is still useful to avoid hitting Postgres on every request.

---

### 5. Frontend flow (summary)

1. User logs in → receives Supabase JWT (valid 1 h).  
2. Each API call adds `Authorization: Bearer <JWT>` to the n8n Gateway URL.  
3. On 401 → refresh token.  
4. On 403 → show upgrade prompt.

---

### 6. When to break the rule

- **Very high throughput endpoints** (>100 r/s) → move the same logic to Kong / Traefik plugin or Cloudflare Worker so you don’t pay n8n execution time.  
- **Per-object permissions** (user can edit only *their* file) → still enforce in the domain workflow with a quick Postgres lookup; the Gateway only ensures “user is allowed to call `editFile` at all”.

---

### TL;DR

- **One Auth Gateway workflow** – single source of truth.  
- **Worker workflows** stay thin and trust the Gateway.  
- **Supabase stores users + packages** – it is queried only once per request (inside the Gateway).





Below is a minimal, copy-paste-ready n8n workflow that does **exactly** what the Auth Gateway must do:

1. Accept an incoming request that contains a JWT in the `Authorization` header  
2. Verify the JWT with the **Supabase JWT secret** (HS256)  
3. Extract `sub` (user id) and optional custom claims (e.g. `role`, `package`)  
4. Reject with 401/403 if token is invalid or user is not allowed  
5. Forward the request (with the decoded payload) to the internal worker workflow

---

### 0. Prerequisites

- You have the **JWT secret** used by your self-hosted Supabase.  
  It is the value of `JWT_SECRET` in your `.env` (looks like a 64-char hex string).  
- The JWT is sent as `Authorization: Bearer <token>`.

---

### 1. Import the workflow JSON

Save the JSON below as `auth-gateway.json` and import it into n8n via **Workflow → Import**.

```json
{
  "name": "Auth Gateway",
  "nodes": [
    {
      "parameters": { "path": "gate/{workflow}" },
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [250, 300],
      "webhookId": "gate"
    },
    {
      "parameters": {
        "functionCode": "const jwt = require('jsonwebtoken');\nconst JWT_SECRET = $env['SUPABASE_JWT_SECRET'];\n\nconst hdr = items[0].json.headers.authorization;\nif (!hdr || !hdr.startsWith('Bearer ')) {\n  throw new Error('MISSING_TOKEN');\n}\nconst token = hdr.replace('Bearer ', '');\n\ntry {\n  const decoded = jwt.verify(token, JWT_SECRET, { algorithms: ['HS256'] });\n  return [{ json: decoded }];\n} catch (err) {\n  throw new Error('INVALID_TOKEN');\n}"
      },
      "name": "Verify JWT",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [450, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json[\"role\"]}}",
              "operation": "notEqual",
              "value2": "authenticated"
            }
          ]
        }
      },
      "name": "Role Check",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [650, 300]
    },
    {
      "parameters": {
        "options": { "responseCode": 401 },
        "respondWith": "={ \"error\": \"unauthorized\" }"
      },
      "name": "Reject 401",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [650, 450]
    },
    {
      "parameters": {
        "url": "={{$env['N8N_INTERNAL_URL']}}/webhook/{{$parameter['workflow']}}",
        "method": "POST",
        "body": "={{ JSON.stringify({ user: $json, original: $json['body'] }) }}",
        "headers": { "x-internal-secret": "={{$env['N8N_SHARED_SECRET']}}" }
      },
      "name": "Forward to Worker",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 1,
      "position": [850, 300]
    },
    {
      "parameters": {
        "options": {},
        "respondWith": "={{$json}}"
      },
      "name": "Return to Caller",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1050, 300]
    }
  ],
  "connections": {
    "Webhook": { "main": [[{ "node": "Verify JWT", "type": "main", "index": 0 }]] },
    "Verify JWT": { "main": [[{ "node": "Role Check", "type": "main", "index": 0 }]] },
    "Role Check": {
      "main": [
        [{ "node": "Forward to Worker", "type": "main", "index": 0 }],
        [{ "node": "Reject 401", "type": "main", "index": 0 }]
      ]
    },
    "Forward to Worker": { "main": [[{ "node": "Return to Caller", "type": "main", "index": 0 }]] }
  },
  "active": true,
  "settings": {
    "saveExecutionProgress": false,
    "saveManualExecutions": false
  }
}
```

---

### 2. Environment variables to set in n8n

| Variable              | Where it comes from |
|-----------------------|---------------------|
| `SUPABASE_JWT_SECRET` | Copy from your Supabase `.env` file (key = `JWT_SECRET`) |
| `N8N_INTERNAL_URL`    | Internal URL your worker workflows listen on (e.g. `http://n8n-private:5678`) |
| `N8N_SHARED_SECRET`   | Any random string you invent. Worker workflows check for it in `x-internal-secret` to refuse outside calls. |

Set them either in the Coolify UI or via `docker-compose.yml`:

```yaml
environment:
  - SUPABASE_JWT_SECRET=your-64-char-secret
  - N8N_INTERNAL_URL=http://n8n-private:5678
  - N8N_SHARED_SECRET=super-secret-internal-token
```

---

### 3. How the flow works step-by-step

1. **Webhook** receives `POST /gate/createProject` (any path segment becomes the workflow variable).  
2. **Function → Verify JWT**  
   - Pulls `Authorization: Bearer <token>`  
   - Uses the HS256 secret to verify and decode → `{ sub: 'uuid', role: 'authenticated', … }`  
3. **IF node**  
   - Example rule shown: deny if `role !== 'authenticated'`  
   - Add as many IF nodes as you need (package limits, feature flags, etc.).  
4. **Reject 401** immediately responds 401 → caller never hits the worker.  
5. **Forward to Worker** sends the decoded user object + the original body to the internal workflow.  
6. **Return to Caller** pipes the worker’s response back to the frontend.

---

### 4. Optional: use the built-in JWT node (n8n ≥ 1.0)

If you prefer a UI instead of the Function node:

- Add **Credentials → JWT**  
  - Algorithm: HS256  
  - Secret: `{{$env.SUPABASE_JWT_SECRET}}`  
- Use the **JWT node** set to “Verify”.  
- The rest of the flow stays identical.

---

### 5. Quick test from the frontend

```bash
curl -H "Authorization: Bearer $USER_JWT" \
     -X POST https://your-n8n.com/gate/createProject \
     -d '{"name":"demo"}'
```

- Valid token → 200 OK + worker response  
- Invalid token → 401  
- Token ok but quota exceeded → 403 (add another IF node)

That’s all you need for a secure, centralized Auth Gateway in n8n.



Yes — when you don’t have a frontend yet you can still get a real JWT for any existing Supabase user with a single `curl` call.  
Supabase exposes the same `/auth/v1/token` endpoint that the JS client uses.

---

### 1. Pick an existing user (or create one)

If you don’t have a user yet, create one from the SQL editor in the Supabase dashboard:

```sql
insert into auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, created_at, updated_at)
values (
  '00000000-0000-0000-0000-000000000000',
  gen_random_uuid(),
  'authenticated',
  'authenticated',
  '<EMAIL>',
  crypt('secret', gen_salt('bf')),
  now(),
  now(),
  now()
);
```

---

### 2. Exchange email + password for a JWT

```bash
# For local self-hosted Supabase
SUPABASE_URL="http://localhost:8000"
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIn0.abc123..."

curl -X POST "$SUPABASE_URL/auth/v1/token?grant_type=password" \
     -H "apikey: $ANON_KEY" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"secret"}'
```

Response:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "...",
  "expires_in": 3600,
  "token_type": "Bearer",
  "user": { ... }
}
```

Grab the `access_token` value — that’s your `$USER_JWT`.

---

### 3. Export and use it

```bash
export USER_JWT="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

curl -H "Authorization: Bearer $USER_JWT" \
     -H "Content-Type: application/json" \
     -d '{"name":"demo"}' \
     http://localhost:5678/gate/createProject
```

You now have a valid JWT for testing without any frontend code.
