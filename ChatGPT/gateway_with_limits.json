{"name": "Gateway Workflow with Limits", "nodes": [{"parameters": {"httpMethod": "POST", "path": "gateway", "responseMode": "lastNode"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "select", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}]}, "name": "Fetch User Profile", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "// Get user profile\nconst profile = $json;\nconst today = new Date().toISOString().split('T')[0];\n\n// Check subscription status or trial\nif(profile.subscription_status !== 'active'){\n  if(profile.trial_end_date && new Date(profile.trial_end_date) >= new Date()){\n    // Trial active, allow\n  } else {\n    throw new Error('Subscription inactive or expired');\n  }\n}\n\n// Reset counters if needed\nif(profile.last_reset_date !== today){\n  profile.videos_downloaded_today = 0;\n  profile.videos_downloaded_this_month = 0;\n  profile.last_reset_date = today;\n}\n\n// Enforce limits\nif(profile.max_daily_downloads && profile.videos_downloaded_today >= profile.max_daily_downloads){\n  throw new Error('Daily download limit reached');\n}\nif(profile.max_monthly_downloads && profile.videos_downloaded_this_month >= profile.max_monthly_downloads){\n  throw new Error('Monthly download limit reached');\n}\n\nreturn [{ json: profile }];"}, "name": "Check Subscription and Limits", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"workflowId": "WORKER_WORKFLOW_ID", "dataPropertyName": "inputData"}, "name": "Call Worker Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"operation": "update", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"id\"]}}", "condition": "="}], "updateFields": [{"column": "videos_downloaded_today", "value": "={{$json[\"videos_downloaded_today\"] + 1}}"}, {"column": "videos_downloaded_this_month", "value": "={{$json[\"videos_downloaded_this_month\"] + 1}}"}, {"column": "last_reset_date", "value": "={{$json[\"last_reset_date\"]}}"}]}, "name": "Increment Counters", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1500, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Fetch User Profile", "type": "main", "index": 0}]]}, "Fetch User Profile": {"main": [[{"node": "Check Subscription and Limits", "type": "main", "index": 0}]]}, "Check Subscription and Limits": {"main": [[{"node": "Call Worker Workflow", "type": "main", "index": 0}]]}, "Call Worker Workflow": {"main": [[{"node": "Increment Counters", "type": "main", "index": 0}]]}, "Increment Counters": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}