<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>SaaS Admin Dashboard</title>
<style>
  body { font-family: Arial, sans-serif; background: #f4f6f8; margin: 0; padding: 0; }
  header { background: #1e3a8a; color: white; padding: 1rem; text-align: center; }
  main { padding: 2rem; }
  table { width: 100%; border-collapse: collapse; background: white; }
  th, td { border: 1px solid #ddd; padding: 0.5rem; text-align: left; }
  th { background: #2563eb; color: white; }
  tr:nth-child(even) { background: #f9f9f9; }
  .status-active { color: green; font-weight: bold; }
  .status-inactive { color: red; font-weight: bold; }
</style>
</head>
<body>

<header>
  <h1>SaaS Admin Dashboard</h1>
</header>

<main>
  <h2>Users Overview</h2>
  <table id="usersTable">
    <thead>
      <tr>
        <th>Name</th>
        <th>Email</th>
        <th>Package</th>
        <th>Subscription Status</th>
        <th>Trial Ends</th>
        <th>Videos Today</th>
        <th>Videos This Month</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <!-- User rows will be injected here -->
    </tbody>
  </table>
</main>

<script>
// Example: fetch users from your backend (Supabase / n8n endpoint)
async function fetchUsers() {
  try {
    // Replace with your API endpoint
    const res = await fetch('/api/admin/get-users');
    const users = await res.json();
    const tbody = document.querySelector('#usersTable tbody');
    tbody.innerHTML = '';

    users.forEach(user => {
      const tr = document.createElement('tr');

      tr.innerHTML = `
        <td>${user.name} ${user.surname}</td>
        <td>${user.email}</td>
        <td>${user.package_name}</td>
        <td class="${user.subscription_status === 'active' ? 'status-active' : 'status-inactive'}">${user.subscription_status}</td>
        <td>${user.trial_end_date || '-'}</td>
        <td>${user.videos_downloaded_today || 0}</td>
        <td>${user.videos_downloaded_this_month || 0}</td>
        <td>
          <button onclick="resetCounters('${user.id}')">Reset Counters</button>
          <button onclick="toggleSubscription('${user.id}', '${user.subscription_status}')">${user.subscription_status === 'active' ? 'Deactivate' : 'Activate'}</button>
        </td>
      `;
      tbody.appendChild(tr);
    });
  } catch(err) {
    console.error('Error fetching users:', err);
  }
}

// Example actions
async function resetCounters(userId) {
  await fetch(`/api/admin/reset-counters/${userId}`, { method: 'POST' });
  fetchUsers();
}

async function toggleSubscription(userId, currentStatus) {
  await fetch(`/api/admin/toggle-subscription/${userId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ status: currentStatus === 'active' ? 'inactive' : 'active' })
  });
  fetchUsers();
}

// Initial load
fetchUsers();
</script>

</body>
</html>

