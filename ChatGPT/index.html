<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>SaaS Auth Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .card { border: 1px solid #ccc; border-radius: 10px; padding: 20px; margin-bottom: 20px; }
    input, button { margin: 5px 0; padding: 8px; width: 100%; }
    pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
  </style>
</head>
<body>
  <h1>🔐 SaaS Auth Workflow Test</h1>

  <!-- Login Form -->
  <div class="card">
    <h2>Login</h2>
    <input type="email" id="email" placeholder="Email">
    <input type="password" id="password" placeholder="Password">
    <button onclick="login()">Login</button>
    <p><strong>JWT:</strong></p>
    <pre id="jwt"></pre>
  </div>

  <!-- Worker Call -->
  <div class="card">
    <h2>Call Worker Workflow</h2>
    <button onclick="callWorker()">Call Protected Worker</button>
    <p><strong>Response:</strong></p>
    <pre id="workerResponse"></pre>
  </div>

  <script>
    let jwtToken = "";

    async function login() {
      const email = document.getElementById("email").value;
      const password = document.getElementById("password").value;

      const res = await fetch("http://localhost:5678/webhook/gateway", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, action: "login" })
      });

      const data = await res.json();
      jwtToken = data.token || "";
      document.getElementById("jwt").innerText = jwtToken ? jwtToken : "Login failed";
    }

    async function callWorker() {
      if (!jwtToken) {
        alert("Please login first!");
        return;
      }

      const res = await fetch("http://localhost:5678/webhook/gateway", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + jwtToken
        },
        body: JSON.stringify({ action: "get_usage" })
      });

      const data = await res.json();
      document.getElementById("workerResponse").innerText = JSON.stringify(data, null, 2);
    }
  </script>
</body>
</html>

