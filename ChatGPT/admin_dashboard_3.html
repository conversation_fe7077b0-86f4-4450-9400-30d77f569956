<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">

<div class="container mx-auto py-8">
  <!-- Login Form -->
  <div id="loginForm" class="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
    <h2 class="text-2xl font-bold mb-4">Admin Login</h2>
    <input type="email" id="email" placeholder="Email" class="w-full p-2 mb-2 border rounded" />
    <input type="password" id="password" placeholder="Password" class="w-full p-2 mb-4 border rounded" />
    <button onclick="login()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 w-full">Login</button>
    <p id="loginError" class="text-red-500 mt-2"></p>
  </div>

  <!-- Dashboard -->
  <div id="dashboard" class="hidden">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Admin Dashboard</h1>
      <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">Logout</button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white p-4 rounded shadow"><p class="font-bold">Total Users</p><p id="totalUsers" class="text-xl">0</p></div>
      <div class="bg-white p-4 rounded shadow"><p class="font-bold">Active Subscriptions</p><p id="activeSubs" class="text-xl">0</p></div>
      <div class="bg-white p-4 rounded shadow"><p class="font-bold">Total Storage Used (MB)</p><p id="totalStorage" class="text-xl">0</p></div>
      <div class="bg-white p-4 rounded shadow"><p class="font-bold">Total Bandwidth Used (GB)</p><p id="totalBandwidth" class="text-xl">0</p></div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-white p-4 rounded shadow">
        <h2 class="font-bold mb-2">Storage Usage</h2>
        <canvas id="storageChart"></canvas>
      </div>
      <div class="bg-white p-4 rounded shadow">
        <h2 class="font-bold mb-2">Bandwidth Usage</h2>
        <canvas id="bandwidthChart"></canvas>
      </div>
    </div>

    <!-- Packages Table -->
    <div class="bg-white p-4 rounded shadow mb-6">
      <h2 class="font-bold mb-2">Packages</h2>
      <table class="min-w-full border-collapse">
        <thead class="bg-gray-200">
          <tr>
            <th class="p-2 border">Name</th>
            <th class="p-2 border">Price</th>
            <th class="p-2 border">Daily Videos</th>
            <th class="p-2 border">Storage Limit (GB)</th>
          </tr>
        </thead>
        <tbody id="packagesTable"></tbody>
      </table>
    </div>

    <!-- Users Table -->
    <div class="bg-white p-4 rounded shadow">
      <h2 class="font-bold mb-2">Users</h2>
      <table class="min-w-full border-collapse">
        <thead class="bg-gray-200">
          <tr>
            <th class="p-2 border">Email</th>
            <th class="p-2 border">Role</th>
            <th class="p-2 border">Package</th>
            <th class="p-2 border">Subscription</th>
            <th class="p-2 border">Storage (MB)</th>
            <th class="p-2 border">Bandwidth (MB)</th>
          </tr>
        </thead>
        <tbody id="usersTable"></tbody>
      </table>
    </div>
  </div>
</div>

<script>
const SUPABASE_URL = "https://YOUR-PROJECT.supabase.co";
const SUPABASE_ANON_KEY = "YOUR_SUPABASE_ANON_KEY";
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function login() {
  const email = document.getElementById("email").value;
  const password = document.getElementById("password").value;
  const { data, error } = await supabase.auth.signInWithPassword({ email, password });
  if (error) {
    document.getElementById("loginError").innerText = error.message;
    return;
  }
  localStorage.setItem("jwt", data.session.access_token);
  loadDashboard();
}

async function loadDashboard() {
  const token = localStorage.getItem("jwt");
  if (!token) return;

  try {
    const res = await fetch("http://localhost:5678/webhook/admin_dashboard", {
      headers: { "Authorization": "Bearer " + token }
    });
    const json = await res.json();
    if (json.error) {
      document.getElementById("loginError").innerText = json.error;
      logout();
      return;
    }

    document.getElementById("loginForm").style.display = "none";
    document.getElementById("dashboard").style.display = "block";

    // Stats
    document.getElementById("totalUsers").innerText = json.stats.total_users;
    document.getElementById("activeSubs").innerText = json.stats.active_subscriptions;
    document.getElementById("totalStorage").innerText = json.stats.total_storage_mb;
    document.getElementById("totalBandwidth").innerText = json.stats.total_bandwidth_gb;

    // Packages table
    const packagesTable = document.getElementById("packagesTable");
    packagesTable.innerHTML = "";
    json.packages.forEach(p => {
      packagesTable.innerHTML += `<tr>
        <td class="p-2 border">${p.name}</td>
        <td class="p-2 border">$${p.price_monthly}</td>
        <td class="p-2 border">${p.video_downloads_per_day}</td>
        <td class="p-2 border">${p.storage_limit_gb}</td>
      </tr>`;
    });

    // Users table
    const usersTable = document.getElementById("usersTable");
    usersTable.innerHTML = "";
    json.users.forEach(u => {
      usersTable.innerHTML += `<tr>
        <td class="p-2 border">${u.email}</td>
        <td class="p-2 border">${u.role}</td>
        <td class="p-2 border">${u.package_name}</td>
        <td class="p-2 border">${u.subscription_status}</td>
        <td class="p-2 border">${u.total_storage_used_mb}</td>
        <td class="p-2 border">${u.total_bandwidth_used_mb}</td>
      </tr>`;
    });

    // Charts
    new Chart(document.getElementById("storageChart"), {
      type: "doughnut",
      data: {
        labels: json.users.map(u => u.email),
        datasets: [{
          label: "Storage Used (MB)",
          data: json.users.map(u => u.total_storage_used_mb),
          backgroundColor: json.users.map((_, i) => `hsl(${i*50 % 360},70%,50%)`)
        }]
      }
    });

    new Chart(document.getElementById("bandwidthChart"), {
      type: "doughnut",
      data: {
        labels: json.users.map(u => u.email),
        datasets: [{
          label: "Bandwidth Used (MB)",
          data: json.users.map(u => u.total_bandwidth_used_mb),
          backgroundColor: json.users.map((_, i) => `hsl(${i*50 % 360},70%,50%)`)
        }]
      }
    });

  } catch(err) {
    document.getElementById("loginError").innerText = "Failed to load dashboard: " + err.message;
  }
}

function logout() {
  localStorage.removeItem("jwt");
  document.getElementById("loginForm").style.display = "block";
  document.getElementById("dashboard").style.display = "none";
}

// Auto-load if logged in
loadDashboard();
</script>

</body>
</html>

