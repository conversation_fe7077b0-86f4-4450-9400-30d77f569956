{
  "name": "Admin Dashboard API",
  "nodes": [
    {
      "parameters": {
        "path": "admin_dashboard",
        "options": {}
      },
      "id": "1",
      "name": "Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [250, 250],
      "webhookDescription": "Admin dashboard data"
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT u.id, u.email, p.role, p.subscription_status, p.package_id, p.total_storage_used_mb, p.total_bandwidth_used_mb, pk.name AS package_name, pk.price_monthly, pk.video_downloads_per_day, pk.storage_limit_gb\nFROM users u\nJOIN user_profiles p ON u.id = p.user_id\nJOIN packages pk ON pk.id = p.package_id;"
      },
      "id": "2",
      "name": "Fetch Users + Packages",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [500, 200],
      "credentials": {
        "postgres": "Supabase Postgres"
      }
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT COUNT(*) AS total_users,\n       SUM(CASE WHEN p.subscription_status = 'active' THEN 1 ELSE 0 END) AS active_subscriptions,\n       COALESCE(SUM(p.total_storage_used_mb),0) AS total_storage_mb,\n       COALESCE(SUM(p.total_bandwidth_used_mb)/1024.0,0) AS total_bandwidth_gb\nFROM user_profiles p;"
      },
      "id": "3",
      "name": "Fetch Stats",
      "type": "n8n-nodes-base.postgres",
      "typeVersion": 2,
      "position": [500, 400],
      "credentials": {
        "postgres": "Supabase Postgres"
      }
    },
    {
      "parameters": {
        "functionCode": "const users = items[0].json;\nconst stats = items[1].json[0];\n\nreturn [{\n  json: {\n    stats: {\n      total_users: Number(stats.total_users),\n      active_subscriptions: Number(stats.active_subscriptions),\n      total_storage_mb: Number(stats.total_storage_mb),\n      total_bandwidth_gb: Number(stats.total_bandwidth_gb)\n    },\n    packages: users.map(u => ({\n      name: u.package_name,\n      price_monthly: u.price_monthly,\n      video_downloads_per_day: u.video_downloads_per_day,\n      storage_limit_gb: u.storage_limit_gb\n    })).filter((v,i,a)=>a.findIndex(t=>t.name===v.name)===i),\n    users: users.map(u => ({\n      email: u.email,\n      role: u.role,\n      package_name: u.package_name,\n      subscription_status: u.subscription_status,\n      total_storage_used_mb: u.total_storage_used_mb,\n      total_bandwidth_used_mb: u.total_bandwidth_used_mb\n    }))\n  }\n}];"
      },
      "id": "4",
      "name": "Build JSON",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [750, 300]
    },
    {
      "parameters": {
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "5",
      "name": "Respond",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [950, 300]
    }
  ],
  "connections": {
    "Webhook": {
      "main": [
        [
          {
            "node": "Fetch Users + Packages",
            "type": "main",
            "index": 0
          },
          {
            "node": "Fetch Stats",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Fetch Users + Packages": {
      "main": [
        [
          {
            "node": "Build JSON",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Fetch Stats": {
      "main": [
        [
          {
            "node": "Build JSON",
            "type": "main",
            "index": 1
          }
        ]
      ]
    },
    "Build JSON": {
      "main": [
        [
          {
            "node": "Respond",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  ]
}

