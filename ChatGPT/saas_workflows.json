[{"name": "Gateway Workflow with Limits", "nodes": [{"parameters": {"httpMethod": "POST", "path": "gateway", "responseMode": "lastNode"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "select", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}]}, "name": "Fetch User Profile", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "// Subscription & limit enforcement\nconst profile = $json;\nconst today = new Date().toISOString().split('T')[0];\n\nif(profile.subscription_status !== 'active'){\n  if(profile.trial_end_date && new Date(profile.trial_end_date) >= new Date()){\n  } else {\n    throw new Error('Subscription inactive or expired');\n  }\n}\n\nif(profile.last_reset_date !== today){\n  profile.videos_downloaded_today = 0;\n  profile.videos_downloaded_this_month = 0;\n  profile.last_reset_date = today;\n}\n\nif(profile.max_daily_downloads && profile.videos_downloaded_today >= profile.max_daily_downloads){\n  throw new Error('Daily download limit reached');\n}\nif(profile.max_monthly_downloads && profile.videos_downloaded_this_month >= profile.max_monthly_downloads){\n  throw new Error('Monthly download limit reached');\n}\nreturn [{ json: profile }];"}, "name": "Check Subscription and Limits", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"workflowId": "WORKER_WORKFLOW_ID", "dataPropertyName": "inputData"}, "name": "Call Worker Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"operation": "update", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"id\"]}}", "condition": "="}], "updateFields": [{"column": "videos_downloaded_today", "value": "={{$json[\"videos_downloaded_today\"] + 1}}"}, {"column": "videos_downloaded_this_month", "value": "={{$json[\"videos_downloaded_this_month\"] + 1}}"}, {"column": "last_reset_date", "value": "={{$json[\"last_reset_date\"]}}"}]}, "name": "Increment Counters", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1250, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1500, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Fetch User Profile", "type": "main", "index": 0}]]}, "Fetch User Profile": {"main": [[{"node": "Check Subscription and Limits", "type": "main", "index": 0}]]}, "Check Subscription and Limits": {"main": [[{"node": "Call Worker Workflow", "type": "main", "index": 0}]]}, "Call Worker Workflow": {"main": [[{"node": "Increment Counters", "type": "main", "index": 0}]]}, "Increment Counters": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}, {"name": "Worker Workflow Template", "nodes": [{"parameters": {"httpMethod": "POST", "path": "worker", "responseMode": "lastNode"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"functionCode": "// Sample worker task\nreturn [{ json: { message: 'Worker executed successfully', input: $json.inputData } }];"}, "name": "Worker Task", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Worker Task", "type": "main", "index": 0}]]}, "Worker Task": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}, {"name": "Get Users", "nodes": [{"parameters": {"httpMethod": "GET", "path": "get-users"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "select", "table": "user_profiles", "returnAll": true, "filters": []}, "name": "Supabase Get Users", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Supabase Get Users", "type": "main", "index": 0}]]}, "Supabase Get Users": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}, {"name": "Reset Counters", "nodes": [{"parameters": {"httpMethod": "POST", "path": "reset-counters/:userId"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "update", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}], "updateFields": [{"column": "videos_downloaded_today", "value": "0"}, {"column": "videos_downloaded_this_month", "value": "0"}]}, "name": "Supabase Update Counters", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Supabase Update Counters", "type": "main", "index": 0}]]}, "Supabase Update Counters": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}, {"name": "Toggle Subscription", "nodes": [{"parameters": {"httpMethod": "POST", "path": "toggle-subscription/:userId"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "update", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}], "updateFields": [{"column": "subscription_status", "value": "={{$json[\"status\"]}}"}]}, "name": "Supabase Update Status", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [750, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Supabase Update Status", "type": "main", "index": 0}]]}, "Supabase Update Status": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}]