{"name": "Gateway Workflow (DB-backed)", "nodes": [{"parameters": {"path": "gateway", "options": {}}, "id": "Webhook_Entry", "name": "Webhook Entry", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 250]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM auth.users WHERE id = $1", "values": "={{ [$json[\"headers\"][\"x-user-id\"]] }}"}, "id": "Fetch_User", "name": "Fetch User", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [500, 250], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO logs (user_id, action, status, details) VALUES ($1, $2, $3, $4)", "values": "={{ [$json[\"headers\"][\"x-user-id\"], \"api_request\", \"pending\", $json] }}"}, "id": "Insert_Log", "name": "Insert Log", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [750, 400], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE rate_limits SET requests_last_minute = requests_last_minute + 1, last_request = NOW() WHERE user_id = $1 RETURNING requests_last_minute", "values": "={{ [$json[\"headers\"][\"x-user-id\"]] }}"}, "id": "Update_RateLimit", "name": "Update RateLimit", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [750, 100], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"functionCode": "const count = $json[\"rows\"][0]?.requests_last_minute || 0;\nif (count > 30) {\n  return [{ blocked: true, reason: \"Rate limit exceeded\" }];\n}\nreturn [{ blocked: false }];"}, "id": "Check_RateLimit", "name": "Check RateLimit", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1000, 100]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM user_usage WHERE user_id = $1", "values": "={{ [$json[\"headers\"][\"x-user-id\"]] }}"}, "id": "Fetch_Usage", "name": "Fetch Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1000, 250], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"functionCode": "const usage = $json[\"rows\"][0];\nconst limits = {\n  max_videos_day: 5,\n  max_videos_month: 50,\n  max_storage_mb: 1024,\n  max_bandwidth_mb: 2048\n};\n\nif (usage.videos_downloaded_today >= limits.max_videos_day) {\n  return [{ blocked: true, reason: \"Daily video quota exceeded\" }];\n}\nif (usage.videos_downloaded_this_month >= limits.max_videos_month) {\n  return [{ blocked: true, reason: \"Monthly video quota exceeded\" }];\n}\nif (usage.total_storage_used_mb >= limits.max_storage_mb) {\n  return [{ blocked: true, reason: \"Storage limit exceeded\" }];\n}\nif (usage.total_bandwidth_used_mb >= limits.max_bandwidth_mb) {\n  return [{ blocked: true, reason: \"Bandwidth limit exceeded\" }];\n}\n\nreturn [{ blocked: false }];"}, "id": "Check_Usage", "name": "Check Usage", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1250, 250]}, {"parameters": {"url": "http://localhost:5678/webhook/worker", "method": "POST", "sendBody": true, "bodyParameters": {"user_id": "={{ $json[\"headers\"][\"x-user-id\"]] }}", "payload": "={{ $json }}"}}, "id": "Call_Worker", "name": "Call Worker Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1500, 250]}, {"parameters": {"responseMode": "lastNode", "options": {}}, "id": "Return_Response", "name": "Return Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1750, 250]}], "connections": {"Webhook Entry": {"main": [[{"node": "Fetch_User", "type": "main", "index": 0}, {"node": "Insert_Log", "type": "main", "index": 0}, {"node": "Update_RateLimit", "type": "main", "index": 0}]]}, "Update_RateLimit": {"main": [[{"node": "Check_RateLimit", "type": "main", "index": 0}]]}, "Check_RateLimit": {"main": [[{"node": "Fetch_Usage", "type": "main", "index": 0}]]}, "Fetch_Usage": {"main": [[{"node": "Check_Usage", "type": "main", "index": 0}]]}, "Check_Usage": {"main": [[{"node": "Call_Worker", "type": "main", "index": 0}]]}, "Call_Worker": {"main": [[{"node": "Return_Response", "type": "main", "index": 0}]]}}}