{"name": "Stripe Webhook", "nodes": [{"parameters": {"httpMethod": "POST", "path": "stripe-webhook", "responseMode": "lastNode"}, "name": "<PERSON>e Webhook Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"functionCode": "const event = $json;\nswitch(event.type){\n  case 'checkout.session.completed':\n    return [{ json: { userId: event.data.object.client_reference_id, status: 'active', sessionId: event.data.object.id } }];\n  case 'invoice.payment_failed':\n    return [{ json: { userId: event.data.object.customer_email, status: 'payment_failed' } }];\n  case 'customer.subscription.deleted':\n    return [{ json: { userId: event.data.object.customer_email, status: 'expired' } }];\n  default:\n    return [];\n}"}, "name": "Parse Stripe Event", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"operation": "update", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}], "updateFields": [{"column": "subscription_status", "value": "={{$json[\"status\"]}}"}]}, "name": "Update Subscription Status", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"responseBody": {"received": true}}, "name": "Respond OK", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1000, 300]}], "connections": {"Stripe Webhook Trigger": {"main": [[{"node": "Parse Stripe Event", "type": "main", "index": 0}]]}, "Parse Stripe Event": {"main": [[{"node": "Update Subscription Status", "type": "main", "index": 0}]]}, "Update Subscription Status": {"main": [[{"node": "Respond OK", "type": "main", "index": 0}]]}}}