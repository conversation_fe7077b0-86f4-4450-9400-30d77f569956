<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Login / Registration</title>
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
<style>
  body { font-family: Arial; background: #f4f6f8; display: flex; justify-content: center; align-items: center; height: 100vh; }
  .auth-container { background: white; padding: 2rem; border-radius: 8px; width: 350px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
  input, button { width: 100%; margin: 0.5rem 0; padding: 0.5rem; }
  button { background: #2563eb; color: white; border: none; cursor: pointer; }
</style>
</head>
<body>

<div class="auth-container">
  <h2>Login / Register</h2>

  <input type="email" id="email" placeholder="Email">
  <input type="password" id="password" placeholder="Password">
  <button onclick="login()">Login</button>
  <button onclick="register()">Register</button>
  
  <hr>
  <button onclick="signInWithGoogle()">Login with Google</button>
</div>

<script>
  const supabaseUrl = 'YOUR_SUPABASE_URL';
  const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
  const supabase = supabase.createClient(supabaseUrl, supabaseKey);

  async function register() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signUp({ email, password });
    if(error) return alert(error.message);
    alert('Registration successful! Please verify your email.');
  }

  async function login() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if(error) return alert(error.message);
    if(!data.user.email_confirmed_at) return alert('Please verify your email first!');
    // Check if 2FA is enabled
    if(data.user.phone && !data.user.phone_verified) alert('2FA verification required.');
    else window.location.href = '/admin_dashboard.html';
  }

  async function signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({ provider: 'google' });
    if(error) return alert(error.message);
  }

  // Listen for auth state changes
  supabase.auth.onAuthStateChange((event, session) => {
    if(session) window.location.href = '/admin_dashboard.html';
  });
</script>

</body>
</html>

