<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>SaaS Admin SPA</title>
<script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
<style>
  body { font-family: Arial; background: #f4f6f8; margin:0; }
  header { background: #1e3a8a; color: white; padding: 1rem; text-align: center; }
  .container { padding: 2rem; }
  .hidden { display: none; }
  input, button { width: 100%; margin: 0.5rem 0; padding: 0.5rem; }
  button { background: #2563eb; color: white; border: none; cursor: pointer; }
  table { width: 100%; border-collapse: collapse; background: white; margin-top: 1rem; }
  th, td { border: 1px solid #ddd; padding: 0.5rem; text-align: left; }
  th { background: #2563eb; color: white; }
  tr:nth-child(even) { background: #f9f9f9; }
  .status-active { color: green; font-weight: bold; }
  .status-inactive { color: red; font-weight: bold; }
</style>
</head>
<body>

<header>
  <h1>SaaS Admin SPA</h1>
</header>

<div class="container">
  <!-- Login / Registration -->
  <div id="authView">
    <h2>Login / Register</h2>
    <input type="email" id="email" placeholder="Email">
    <input type="password" id="password" placeholder="Password">
    <button onclick="login()">Login</button>
    <button onclick="register()">Register</button>
    <hr>
    <button onclick="signInWithGoogle()">Login with Google</button>
  </div>

  <!-- Dashboard -->
  <div id="dashboardView" class="hidden">
    <h2>Users Overview</h2>
    <button onclick="logout()">Logout</button>
    <table id="usersTable">
      <thead>
        <tr>
          <th>Name</th><th>Email</th><th>Package</th><th>Subscription</th>
          <th>Trial Ends</th><th>Videos Today</th><th>Videos This Month</th><th>Actions</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>
</div>

<script>
  const supabaseUrl = 'YOUR_SUPABASE_URL';
  const supabaseKey = 'YOUR_SUPABASE_ANON_KEY';
  const supabase = supabase.createClient(supabaseUrl, supabaseKey);

  // Auth functions
  async function register() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signUp({ email, password });
    if(error) return alert(error.message);
    alert('Registration successful! Please verify your email.');
  }

  async function login() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    if(error) return alert(error.message);
    if(!data.user.email_confirmed_at) return alert('Please verify your email!');
    showDashboard();
  }

  async function signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({ provider: 'google' });
    if(error) return alert(error.message);
  }

  async function logout() {
    await supabase.auth.signOut();
    document.getElementById('dashboardView').classList.add('hidden');
    document.getElementById('authView').classList.remove('hidden');
  }

  // SPA view switch
  function showDashboard() {
    document.getElementById('authView').classList.add('hidden');
    document.getElementById('dashboardView').classList.remove('hidden');
    fetchUsers();
  }

  // Fetch and display users
  async function fetchUsers() {
    try {
      // Replace with your n8n / Supabase endpoint
      const res = await fetch('/api/admin/get-users');
      const users = await res.json();
      const tbody = document.querySelector('#usersTable tbody');
      tbody.innerHTML = '';
      users.forEach(user => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${user.name} ${user.surname}</td>
          <td>${user.email}</td>
          <td>${user.package_name}</td>
          <td class="${user.subscription_status==='active'?'status-active':'status-inactive'}">${user.subscription_status}</td>
          <td>${user.trial_end_date||'-'}</td>
          <td>${user.videos_downloaded_today||0}</td>
          <td>${user.videos_downloaded_this_month||0}</td>
          <td>
            <button onclick="resetCounters('${user.id}')">Reset Counters</button>
            <button onclick="toggleSubscription('${user.id}', '${user.subscription_status}')">
              ${user.subscription_status==='active'?'Deactivate':'Activate'}
            </button>
          </td>
        `;
        tbody.appendChild(tr);
      });
    } catch(err) {
      console.error(err);
    }
  }

  // Admin actions
  async function resetCounters(userId) {
    await fetch(`/api/admin/reset-counters/${userId}`, { method:'POST' });
    fetchUsers();
  }

  async function toggleSubscription(userId, currentStatus) {
    await fetch(`/api/admin/toggle-subscription/${userId}`, {
      method:'POST',
      headers:{ 'Content-Type':'application/json' },
      body: JSON.stringify({ status: currentStatus==='active'?'inactive':'active' })
    });
    fetchUsers();
  }

  // Check auth state on load
  supabase.auth.onAuthStateChange((event, session) => {
    if(session) showDashboard();
  });
</script>

</body>
</html>

