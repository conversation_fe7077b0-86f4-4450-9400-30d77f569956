<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Dashboard</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f6fa; }
    h1 { margin-bottom: 10px; }
    .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 6px rgba(0,0,0,0.1); }
    table { width: 100%; border-collapse: collapse; margin-top: 10px; }
    th, td { padding: 8px; border-bottom: 1px solid #ddd; text-align: left; }
    th { background: #f0f0f0; }
    pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .stat { font-size: 1.4em; font-weight: bold; }
  </style>
</head>
<body>
  <h1>📊 Admin Dashboard</h1>

  <!-- Global Stats -->
  <div class="grid">
    <div class="card">
      <h2>Global Stats</h2>
      <p>Total Users: <span class="stat" id="totalUsers">0</span></p>
      <p>Active Subscriptions: <span class="stat" id="activeSubs">0</span></p>
      <p>Total Storage Used: <span class="stat" id="totalStorage">0 MB</span></p>
      <p>Total Bandwidth Used: <span class="stat" id="totalBandwidth">0 GB</span></p>
    </div>

    <div class="card">
      <h2>Packages</h2>
      <table id="packagesTable">
        <thead>
          <tr><th>Name</th><th>Price</th><th>Limits</th></tr>
        </thead>
        <tbody></tbody>
      </table>
    </div>
  </div>

  <!-- Users -->
  <div class="card" style="margin-top:20px;">
    <h2>Users</h2>
    <table id="usersTable">
      <thead>
        <tr><th>Email</th><th>Role</th><th>Package</th><th>Status</th><th>Storage</th><th>Bandwidth</th></tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <!-- Raw API Debug -->
  <div class="card" style="margin-top:20px;">
    <h2>Raw API Response</h2>
    <pre id="rawResponse">Waiting for data...</pre>
  </div>

  <script>
    async function loadData() {
      try {
        // Call your n8n admin workflow
        const res = await fetch("http://localhost:5678/webhook/admin_dashboard");
        const data = await res.json();

        // Debug view
        document.getElementById("rawResponse").innerText = JSON.stringify(data, null, 2);

        // Stats
        document.getElementById("totalUsers").innerText = data.stats.total_users;
        document.getElementById("activeSubs").innerText = data.stats.active_subscriptions;
        document.getElementById("totalStorage").innerText = data.stats.total_storage_mb + " MB";
        document.getElementById("totalBandwidth").innerText = data.stats.total_bandwidth_gb + " GB";

        // Packages
        const packagesTable = document.querySelector("#packagesTable tbody");
        packagesTable.innerHTML = "";
        data.packages.forEach(pkg => {
          packagesTable.innerHTML += `
            <tr>
              <td>${pkg.name}</td>
              <td>$${pkg.price_monthly}/mo</td>
              <td>${pkg.video_downloads_per_day} videos/day, ${pkg.storage_limit_gb} GB storage</td>
            </tr>`;
        });

        // Users
        const usersTable = document.querySelector("#usersTable tbody");
        usersTable.innerHTML = "";
        data.users.forEach(u => {
          usersTable.innerHTML += `
            <tr>
              <td>${u.email}</td>
              <td>${u.role}</td>
              <td>${u.package_name}</td>
              <td>${u.subscription_status}</td>
              <td>${u.total_storage_used_mb} MB</td>
              <td>${u.total_bandwidth_used_mb} MB</td>
            </tr>`;
        });

      } catch (err) {
        document.getElementById("rawResponse").innerText = "Error: " + err;
      }
    }

    // Auto-load on page open
    loadData();
  </script>
</body>
</html>

