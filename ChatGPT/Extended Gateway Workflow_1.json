{"name": "Gateway Workflow Full Enhanced", "nodes": [{"parameters": {"httpMethod": "POST", "path": "gateway", "responseMode": "lastNode"}, "name": "HTTP Request", "type": "n8n-nodes-base.httpRequestTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"functionCode": "// Initialize workflow-level global variables\nreturn [{ json: {\n  no_of_videos: 0,\n  wait_period_length: 60,\n  max_request_per_minute: 30,\n  soft_quota_percent: 1.05,\n  concurrency_counter: 0,\n  alert_threshold_percent: 0.8\n} }];"}, "name": "Initialize Variables", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Reset daily usage\nUPDATE user_profiles\nSET videos_downloaded_today = 0,\n    last_reset_date = now()::date\nWHERE id = '{{ $json[\"user_id\"] }}'\n  AND (last_reset_date IS NULL OR last_reset_date < now()::date);\n\n-- Reset monthly usage\nUPDATE user_profiles\nSET videos_downloaded_this_month = 0,\n    last_reset_date_month = date_trunc('month', now())::date\nWHERE id = '{{ $json[\"user_id\"] }}'\n  AND (last_reset_date_month IS NULL OR last_reset_date_month < date_trunc('month', now())::date);\n\nSELECT * FROM user_profiles WHERE id = '{{ $json[\"user_id\"] }}';", "returnAll": true}, "name": "Reset Daily & Monthly Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [650, 300], "credentials": {"postgres": "Supabase Postgres"}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT u.id, u.email, p.name AS package_name,\n       p.video_downloads_per_day,\n       p.video_downloads_per_month,\n       p.storage_limit_gb,\n       p.bandwidth_limit_gb,\n       p.api_access,\n       p.concurrent_downloads,\n       p.bulk_download,\n       p.priority_processing,\n       u.videos_downloaded_today,\n       u.videos_downloaded_this_month,\n       u.total_storage_used_mb,\n       u.total_bandwidth_used_mb\nFROM user_profiles u\nJOIN packages p ON u.package_id = p.id\nWHERE u.id = '{{ $json[\"user_id\"] }}';", "returnAll": true}, "name": "Get User Package", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [850, 300], "credentials": {"postgres": "Supabase Postgres"}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json[\"videos_downloaded_today\"] }}", "operation": "<", "value2": "={{ $json[\"video_downloads_per_day\"] * $json[\"soft_quota_percent\"] }}"}, {"value1": "={{ $json[\"videos_downloaded_this_month\"] }}", "operation": "<", "value2": "={{ $json[\"video_downloads_per_month\"] * $json[\"soft_quota_percent\"] }}"}, {"value1": "={{ $json[\"total_storage_used_mb\"] + $json[\"file_size_mb\"] }}", "operation": "<=", "value2": "={{ $json[\"storage_limit_gb\"] * 1024 }}"}, {"value1": "={{ $json[\"total_bandwidth_used_mb\"] + $json[\"file_size_mb\"] }}", "operation": "<=", "value2": "={{ $json[\"bandwidth_limit_gb\"] * 1024 }}"}, {"value1": "={{ $json[\"no_of_videos\"] }}", "operation": "<", "value2": "={{ $json[\"max_request_per_minute\"] }}"}, {"value1": "={{ $json[\"concurrency_counter\"] }}", "operation": "<", "value2": "={{ $json[\"concurrent_downloads\"] }}"}], "boolean": [{"value1": "={{ $json[\"api_access\"] }}", "operation": "equal", "value2": true}]}}, "name": "Check Quotas & Limits", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1050, 300]}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/worker", "bodyParametersUi": {"parameter": [{"name": "user_id", "value": "={{ $json[\"id\"] }}"}, {"name": "file_size_mb", "value": "={{ $json[\"file_size_mb\"] }}"}]}}, "name": "Call Worker", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1250, 250]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE user_profiles\nSET videos_downloaded_today = videos_downloaded_today + 1,\n    videos_downloaded_this_month = videos_downloaded_this_month + 1,\n    total_storage_used_mb = total_storage_used_mb + {{ $json[\"file_size_mb\"] }},\n    total_bandwidth_used_mb = total_bandwidth_used_mb + {{ $json[\"file_size_mb\"] }},\n    last_download_date = now()\nWHERE id = '{{ $json[\"user_id\"] }}';"}, "name": "Increment Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1450, 250], "credentials": {"postgres": "Supabase Postgres"}}, {"parameters": {"functionCode": "// Decrement concurrency counter after completion\n$json.concurrency_counter = $json.concurrency_counter - 1;\nreturn [{ json: $json }];"}, "name": "Update Concurrency Counter", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1650, 250]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO api_logs(user_id, endpoint, timestamp, file_size_mb, status)\nVALUES('{{ $json[\"user_id\"] }}', '/gateway', now(), {{ $json[\"file_size_mb\"] }}, 'success');"}, "name": "Log API Call", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1850, 250], "credentials": {"postgres": "Supabase Postgres"}}, {"parameters": {"statusCode": 403, "body": "Quota exceeded or API access denied"}, "name": "Reject Request", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1250, 350]}], "connections": {"HTTP Request": {"main": [[{"node": "Initialize Variables", "type": "main", "index": 0}]]}, "Initialize Variables": {"main": [[{"node": "Reset Daily & Monthly Usage", "type": "main", "index": 0}]]}, "Reset Daily & Monthly Usage": {"main": [[{"node": "Get User Package", "type": "main", "index": 0}]]}, "Get User Package": {"main": [[{"node": "Check Quotas & Limits", "type": "main", "index": 0}]]}, "Check Quotas & Limits": {"main": [[{"node": "Call Worker", "type": "main", "index": 0}], [{"node": "Reject Request", "type": "main", "index": 1}]]}, "Call Worker": {"main": [[{"node": "Increment Usage", "type": "main", "index": 0}]]}, "Increment Usage": {"main": [[{"node": "Update Concurrency Counter", "type": "main", "index": 0}, {"node": "Log API Call", "type": "main", "index": 1}]]}}}