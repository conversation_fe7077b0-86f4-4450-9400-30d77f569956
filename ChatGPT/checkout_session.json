{"name": "Create Stripe Checkout Session", "nodes": [{"parameters": {"httpMethod": "POST", "path": "create-checkout-session", "responseMode": "lastNode"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "select", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}]}, "name": "Get User Email & Package", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"resource": "checkoutSession", "operation": "create", "mode": "subscription", "paymentMethodTypes": ["card"], "lineItemsUi": {"lineItemsValues": [{"price": "={{$json[\"package_priceId\"]}}", "quantity": 1}]}, "customerEmail": "={{$json[\"email\"]}}", "successUrl": "https://yourapp.com/success?session_id={CHECKOUT_SESSION_ID}", "cancelUrl": "https://yourapp.com/cancel"}, "name": "Create Stripe Checkout Session", "type": "n8n-nodes-base.stripe", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"operation": "insert", "table": "payments", "columns": ["user_id", "package_id", "checkout_session_id", "status"], "values": [{"user_id": "={{$json[\"userId\"]}}", "package_id": "={{$json[\"package_id\"]}}", "checkout_session_id": "={{$json[\"id\"]}}", "status": "pending"}]}, "name": "Save Checkout Session", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"responseBody": {"sessionId": "={{$json[\"id\"]}}"}}, "name": "Respond with Session", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1250, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Get User Email & Package", "type": "main", "index": 0}]]}, "Get User Email & Package": {"main": [[{"node": "Create Stripe Checkout Session", "type": "main", "index": 0}]]}, "Create Stripe Checkout Session": {"main": [[{"node": "Save Checkout Session", "type": "main", "index": 0}]]}, "Save Checkout Session": {"main": [[{"node": "Respond with Session", "type": "main", "index": 0}]]}}}