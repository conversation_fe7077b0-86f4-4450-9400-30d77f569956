{"name": "Create Stripe Checkout Session", "nodes": [{"parameters": {"httpMethod": "POST", "path": "create-checkout-session", "responseMode": "lastNode"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "select", "table": "packages", "filters": [{"column": "id", "value": "={{$json[\"packageId\"]}}", "condition": "="}], "returnAll": true}, "name": "Fetch Package", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"operation": "createSession", "mode": "payment", "lineItems": [{"price": "={{$json[0].stripe_price_id}}", "quantity": 1}], "successUrl": "https://yourapp.com/success", "cancelUrl": "https://yourapp.com/cancel"}, "name": "Stripe Checkout", "type": "n8n-nodes-base.stripe", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"responseBody": {"checkoutUrl": "={{$json.url}}"}}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1000, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Fetch Package", "type": "main", "index": 0}]]}, "Fetch Package": {"main": [[{"node": "Stripe Checkout", "type": "main", "index": 0}]]}, "Stripe Checkout": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}