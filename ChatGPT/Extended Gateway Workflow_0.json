{"name": "Extended Gateway Workflow", "nodes": [{"id": "init-vars", "name": "Initialize Global Variables", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [250, 300], "parameters": {"values": {"string": [{"name": "no_of_videos", "value": "0"}, {"name": "wait_period_length", "value": "60"}, {"name": "max_requests_per_minute", "value": "30"}, {"name": "grace_percentage", "value": "5"}, {"name": "alert_threshold", "value": "90"}]}}}, {"id": "auth-check", "name": "Auth Validation (JWT)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [500, 200], "parameters": {"url": "http://localhost:5678/webhook/auth", "method": "POST", "jsonParameters": true, "options": {}, "bodyParametersJson": "{ \"token\": \"={{$json[\"headers\"][\"authorization\"]}}\" }"}}, {"id": "rate-limit", "name": "Check Rate Limit", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 200], "parameters": {"functionCode": "const now = Date.now();\nconst lastReq = $json.last_request || 0;\nconst reqs = $json.requests_last_minute || 0;\n\nif (now - lastReq < 60000 && reqs >= $json.max_requests_per_minute) {\n  throw new Error('Rate limit exceeded. Try again later.');\n}\n\nreturn [{ json: { ...$json, last_request: now, requests_last_minute: reqs + 1 } }];"}}, {"id": "quota-check", "name": "Quo<PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [950, 200], "parameters": {"functionCode": "const pkg = $json.package;\nconst profile = $json.user_profile;\nconst grace = pkg.video_downloads_per_day * ($json.grace_percentage / 100);\n\nif (profile.videos_downloaded_today >= (pkg.video_downloads_per_day + grace)) {\n  throw new Error('Daily quota exceeded');\n}\nif (profile.videos_downloaded_this_month >= pkg.video_downloads_per_month) {\n  throw new Error('Monthly quota exceeded');\n}\nif (profile.total_storage_used_mb >= pkg.storage_limit_gb * 1024) {\n  throw new Error('Storage quota exceeded');\n}\nif (profile.total_bandwidth_used_mb >= pkg.bandwidth_limit_gb * 1024) {\n  throw new Error('Bandwidth quota exceeded');\n}\n\nreturn [{ json: $json }];"}}, {"id": "feature-check", "name": "Feature Flag Check", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1150, 200], "parameters": {"functionCode": "const pkg = $json.package;\n\nif (!pkg.api_access) throw new Error('API Access not allowed for this package');\nif ($json.request_type === 'bulk' && !pkg.bulk_download) throw new Error('Bulk download not allowed');\nif ($json.request_type === 'watermark' && !pkg.custom_watermark) throw new Error('Watermarking not allowed');\n\nreturn [{ json: $json }];"}}, {"id": "alert-threshold", "name": "<PERSON> <PERSON><PERSON>", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1350, 200], "parameters": {"functionCode": "const profile = $json.user_profile;\nconst pkg = $json.package;\n\nconst usage = (profile.videos_downloaded_today / pkg.video_downloads_per_day) * 100;\nif (usage >= $json.alert_threshold) {\n  return [{ json: { ...$json, alert: true } }];\n}\nreturn [{ json: { ...$json, alert: false } }];"}}, {"id": "notify-user", "name": "Send Notification (Optional)", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1550, 100], "disabled": true, "parameters": {"url": "http://localhost:5678/webhook/notify", "method": "POST", "jsonParameters": true, "bodyParametersJson": "{ \"user\": \"={{$json.email}}\", \"message\": \"You are nearing your quota limit.\" }"}}, {"id": "audit-log", "name": "Insert Audit Log", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [1550, 300], "parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO logs(user_id, action, created_at) VALUES($1, $2, NOW())", "values": "={{ [$json.user_id, 'gateway_request'] }}"}}, {"id": "call-worker", "name": "Call Worker Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1750, 200], "parameters": {"url": "http://localhost:5678/webhook/worker", "method": "POST", "jsonParameters": true, "bodyParametersJson": "={{JSON.stringify($json)}}"}}], "connections": {"init-vars": {"main": [[{"node": "auth-check", "type": "main", "index": 0}]]}, "auth-check": {"main": [[{"node": "rate-limit", "type": "main", "index": 0}]]}, "rate-limit": {"main": [[{"node": "quota-check", "type": "main", "index": 0}]]}, "quota-check": {"main": [[{"node": "feature-check", "type": "main", "index": 0}]]}, "feature-check": {"main": [[{"node": "alert-threshold", "type": "main", "index": 0}]]}, "alert-threshold": {"main": [{"node": "notify-user", "type": "main", "index": 0}, {"node": "audit-log", "type": "main", "index": 0}]}, "audit-log": {"main": [[{"node": "call-worker", "type": "main", "index": 0}]]}}}