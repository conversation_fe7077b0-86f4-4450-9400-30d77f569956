{"name": "Worker Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "worker", "responseMode": "lastNode"}, "name": "HTTP Request Trigger", "type": "n8n-nodes-base.httpRequestTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"functionCode": "return [{ json: { message: `Worker executed for user ${$json[\"user_id\"]} with file size ${$json[\"file_size_mb\"]} MB` } }];"}, "name": "Execute Worker Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"HTTP Request Trigger": {"main": [[{"node": "Execute Worker Logic", "type": "main", "index": 0}]]}}}