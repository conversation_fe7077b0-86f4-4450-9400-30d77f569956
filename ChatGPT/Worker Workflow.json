{"name": "Worker Workflow (DB-backed)", "nodes": [{"parameters": {"path": "worker", "options": {}}, "id": "Webhook_Worker", "name": "Webhook Worker", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 250]}, {"parameters": {"functionCode": "const payload = $json[\"body\"].payload;\nconst userId = $json[\"body\"].user_id;\n\n// Simulate some work\n// In real workflow: call your job, download video, process file, etc.\n\nreturn [{\n  user_id: userId,\n  job: payload,\n  video_size_mb: 100, // Example video size\n  bandwidth_used_mb: 120 // Example transfer size\n}];"}, "id": "Do_Work", "name": "Do Work (Simulated)", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 250]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO logs (user_id, action, status, details) VALUES ($1, $2, $3, $4)", "values": "={{ [$json[\"user_id\"], \"process_video\", \"success\", $json] }}"}, "id": "Insert_Success_Log", "name": "Insert Success Log", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [750, 400], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE user_usage SET \n  videos_downloaded_today = videos_downloaded_today + 1,\n  videos_downloaded_this_month = videos_downloaded_this_month + 1,\n  total_storage_used_mb = total_storage_used_mb + $2,\n  total_bandwidth_used_mb = total_bandwidth_used_mb + $3\nWHERE user_id = $1", "values": "={{ [$json[\"user_id\"], $json[\"video_size_mb\"], $json[\"bandwidth_used_mb\"]] }}"}, "id": "Update_Usage", "name": "Update Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [750, 100], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"responseMode": "lastNode", "options": {}}, "id": "Return_Worker_Response", "name": "Return Worker Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1000, 250]}], "connections": {"Webhook Worker": {"main": [[{"node": "Do_Work", "type": "main", "index": 0}]]}, "Do_Work": {"main": [[{"node": "Insert_Success_Log", "type": "main", "index": 0}, {"node": "Update_Usage", "type": "main", "index": 0}, {"node": "Return_Worker_Response", "type": "main", "index": 0}]]}}}