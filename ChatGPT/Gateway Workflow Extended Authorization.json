{"name": "Gateway Workflow", "nodes": [{"parameters": {"httpMethod": "POST", "path": "gateway", "responseMode": "lastNode"}, "name": "HTTP Trigger", "type": "n8n-nodes-base.httpTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "select", "table": "user_profiles", "filters": [{"column": "id", "value": "={{$json[\"userId\"]}}", "condition": "="}]}, "name": "Fetch User Profile", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "const profile = $json;\nif(profile.subscription_status !== 'active'){\n  throw new Error('Subscription inactive or expired');\n}\nreturn [{ json: profile }];"}, "name": "Check Subscription", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [750, 300]}, {"parameters": {"workflowId": "WORKER_WORKFLOW_ID", "dataPropertyName": "inputData"}, "name": "Call Worker Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"responseBody": "={{$json}}"}, "name": "Respond", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1250, 300]}], "connections": {"HTTP Trigger": {"main": [[{"node": "Fetch User Profile", "type": "main", "index": 0}]]}, "Fetch User Profile": {"main": [[{"node": "Check Subscription", "type": "main", "index": 0}]]}, "Check Subscription": {"main": [[{"node": "Call Worker Workflow", "type": "main", "index": 0}]]}, "Call Worker Workflow": {"main": [[{"node": "Respond", "type": "main", "index": 0}]]}}}