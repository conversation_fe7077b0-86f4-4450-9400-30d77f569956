{"name": "Daily Reset Usage Counters", "nodes": [{"parameters": {"triggerTimes": {"item": [{"mode": "everyDay", "hour": 0, "minute": 0}]}}, "id": "<PERSON><PERSON>_Midnight", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [250, 250]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "UPDATE user_usage SET videos_downloaded_today = 0"}, "id": "Reset_Daily_Usage", "name": "Reset Daily Usage", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [500, 250], "credentials": {"postgres": "Supabase DB"}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO logs (user_id, action, status, details)\nSELECT user_id, 'daily_reset', 'success', 'Reset daily counters' FROM user_usage"}, "id": "Log_Reset", "name": "Log Reset", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [750, 250], "credentials": {"postgres": "Supabase DB"}}], "connections": {"Cron Midnight": {"main": [[{"node": "Reset_Daily_Usage", "type": "main", "index": 0}]]}, "Reset Daily Usage": {"main": [[{"node": "Log_Reset", "type": "main", "index": 0}]]}}}