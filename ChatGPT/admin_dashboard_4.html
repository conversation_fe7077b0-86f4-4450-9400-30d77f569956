<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f4f6f9; }
    #loginForm, #dashboard { max-width: 600px; margin: auto; padding: 20px; background: #fff; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
    h2 { margin-bottom: 15px; }
    input { width: 100%; padding: 10px; margin: 8px 0; }
    button { padding: 10px 15px; background: #007bff; border: none; color: white; border-radius: 5px; cursor: pointer; }
    button:hover { background: #0056b3; }
    pre { background: #eee; padding: 10px; overflow-x: auto; }
  </style>
</head>
<body>

<div id="loginForm">
  <h2>Admin Login</h2>
  <input type="email" id="email" placeholder="Email" required />
  <input type="password" id="password" placeholder="Password" required />
  <button onclick="login()">Login</button>
  <p id="loginError" style="color:red;"></p>
</div>

<div id="dashboard" style="display:none;">
  <h2>Admin Dashboard</h2>
  <p><button onclick="logout()">Logout</button></p>
  <h3>Stats</h3>
  <pre id="stats"></pre>
  <h3>Packages</h3>
  <pre id="packages"></pre>
  <h3>Users</h3>
  <pre id="users"></pre>
</div>

<script>
  const SUPABASE_URL = "https://YOUR-PROJECT.supabase.co";
  const SUPABASE_ANON_KEY = "YOUR_SUPABASE_ANON_KEY";
  const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

  async function login() {
    const email = document.getElementById("email").value;
    const password = document.getElementById("password").value;
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });

    if (error) {
      document.getElementById("loginError").innerText = error.message;
      return;
    }

    const token = data.session.access_token;
    localStorage.setItem("jwt", token);
    loadDashboard();
  }

  async function loadDashboard() {
    const token = localStorage.getItem("jwt");
    if (!token) {
      document.getElementById("loginForm").style.display = "block";
      document.getElementById("dashboard").style.display = "none";
      return;
    }

    try {
      const res = await fetch("http://localhost:5678/webhook/admin_dashboard", {
        headers: { "Authorization": "Bearer " + token }
      });
      const json = await res.json();

      if (json.error) {
        document.getElementById("loginError").innerText = json.error;
        logout();
        return;
      }

      document.getElementById("loginForm").style.display = "none";
      document.getElementById("dashboard").style.display = "block";
      document.getElementById("stats").innerText = JSON.stringify(json.stats, null, 2);
      document.getElementById("packages").innerText = JSON.stringify(json.packages, null, 2);
      document.getElementById("users").innerText = JSON.stringify(json.users, null, 2);
    } catch (err) {
      document.getElementById("loginError").innerText = "Failed to load dashboard: " + err.message;
    }
  }

  function logout() {
    localStorage.removeItem("jwt");
    document.getElementById("loginForm").style.display = "block";
    document.getElementById("dashboard").style.display = "none";
  }

  // Auto load if already logged in
  loadDashboard();
</script>

</body>
</html>

