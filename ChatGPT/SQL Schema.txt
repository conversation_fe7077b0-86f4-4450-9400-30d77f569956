-- ===========================================
-- 1. Audit Logs Table
-- ===========================================
CREATE TABLE logs (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    action TEXT NOT NULL,
    status TEXT DEFAULT 'success', -- success / error / blocked
    details JSONB,                 -- optional metadata
    created_at TIMESTAMP DEFAULT NOW()
);

-- Index for fast lookup
CREATE INDEX idx_logs_user_id ON logs(user_id);
CREATE INDEX idx_logs_created_at ON logs(created_at);


-- ===========================================
-- 2. Rate Limit Tracking Table
-- ===========================================
CREATE TABLE rate_limits (
    user_id UUID PRIMARY KEY,
    requests_last_minute INT DEFAULT 0,
    last_request TIMESTAMP DEFAULT NOW()
);


-- ===========================================
-- 3. User Usage Table (Quota Tracking)
-- ===========================================
CREATE TABLE user_usage (
    user_id UUID PRIMARY KEY,
    videos_downloaded_today INT DEFAULT 0,
    videos_downloaded_this_month INT DEFAULT 0,
    total_storage_used_mb BIGINT DEFAULT 0,
    total_bandwidth_used_mb BIGINT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT NOW()
);

-- Reset daily counters at midnight
-- Reset monthly counters on 1st of each month
-- (Can be done with n8n Cron workflow or PostgreSQL cron extension)

