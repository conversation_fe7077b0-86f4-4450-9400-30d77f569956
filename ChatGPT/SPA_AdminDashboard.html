<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>SaaS Admin Dashboard</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
    th { background-color: #f2f2f2; }
    button { padding: 5px 10px; margin: 2px; }
    .charts { display: flex; gap: 30px; flex-wrap: wrap; }
    canvas { background: #fff; border: 1px solid #ddd; padding: 10px; }
  </style>
</head>
<body>

<h1>Admin Dashboard</h1>

<h2>Users</h2>
<table id="usersTable">
  <thead>
    <tr>
      <th>User ID</th>
      <th>Email</th>
      <th>Package</th>
      <th>Subscription</th>
      <th>Daily Downloads</th>
      <th>Monthly Downloads</th>
      <th>Actions</th>
    </tr>
  </thead>
  <tbody></tbody>
</table>

<h2>Analytics</h2>
<div class="charts">
  <canvas id="usageChart" width="400" height="200"></canvas>
  <canvas id="subscriptionChart" width="400" height="200"></canvas>
</div>

<script>
const API_BASE = 'https://n8n.homelynx.qzz.io/'; // replace with your n8n instance

// Fetch all users
async function fetchUsers() {
  const res = await fetch(`${API_BASE}/get-users`);
  const data = await res.json();
  const tbody = document.querySelector('#usersTable tbody');
  tbody.innerHTML = '';
  data.forEach(user => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td>${user.user_id || user.id}</td>
      <td>${user.email}</td>
      <td>${user.package_id}</td>
      <td>${user.subscription_status}</td>
      <td>${user.videos_downloaded_today}</td>
      <td>${user.videos_downloaded_this_month}</td>
      <td>
        <button onclick="resetCounters('${user.id}')">Reset Counters</button>
        <button onclick="toggleSubscription('${user.id}', '${user.subscription_status === 'active' ? 'inactive' : 'active'}')">Toggle Subscription</button>
      </td>
    `;
    tbody.appendChild(row);
  });
  renderAnalytics(data);
}

// Reset counters
async function resetCounters(userId) {
  await fetch(`${API_BASE}/reset-counters/${userId}`, { method: 'POST' });
  fetchUsers();
}

// Toggle subscription
async function toggleSubscription(userId, status) {
  await fetch(`${API_BASE}/toggle-subscription/${userId}`, {
    method: 'POST',
    headers: { 'Content-Type':'application/json' },
    body: JSON.stringify({ status })
  });
  fetchUsers();
}

// Render charts
function renderAnalytics(users) {
  const usageCtx = document.getElementById('usageChart').getContext('2d');
  const subscriptionCtx = document.getElementById('subscriptionChart').getContext('2d');

  const labels = users.map(u => u.email || u.id);
  const daily = users.map(u => u.videos_downloaded_today || 0);
  const monthly = users.map(u => u.videos_downloaded_this_month || 0);

  new Chart(usageCtx, {
    type: 'bar',
    data: { labels, datasets: [{ label:'Daily Downloads', data:daily, backgroundColor:'rgba(75,192,192,0.6)' },{ label:'Monthly Downloads', data:monthly, backgroundColor:'rgba(153,102,255,0.6)' }] }
  });

  const activeCount = users.filter(u => u.subscription_status === 'active').length;
  const inactiveCount = users.length - activeCount;

  new Chart(subscriptionCtx, {
    type: 'doughnut',
    data: { labels:['Active','Inactive'], datasets:[{ data:[activeCount, inactiveCount], backgroundColor:['#36A2EB','#FF6384'] }] }
  });
}

// Initial load
fetchUsers();
</script>
</body>
</html>

