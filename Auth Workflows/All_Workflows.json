[{"name": "Auth & Access Workflow", "nodes": [{"parameters": {"path": "auth-check", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Auth Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "const authHeader = $json[\"headers\"][\"authorization\"];\nif (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n  return [{ error: true, message: \"Missing or invalid Authorization header\" }];\n}\n\nreturn [{ token: authHeader.split(\" \")[1] }];"}, "id": "2", "name": "Extract Token", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "const jwt = require('jsonwebtoken');\ntry {\n  const decoded = jwt.verify($json[\"token\"], $env.SUPABASE_JWT_SECRET);\n  return [{ userId: decoded.sub, email: decoded.email }];\n} catch (err) {\n  return [{ error: true, message: \"Invalid token\" }];\n}"}, "id": "3", "name": "Verify JWT", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}, {"parameters": {"operation": "list", "table": "users", "options": {}, "filters": "{\"id\": \"={{$json[\\\"userId\\\"]}}\"}"}, "id": "4", "name": "Lookup User (NocoDB)", "type": "n8n-nodes-base.nocoDb", "typeVersion": 1, "position": [980, 300], "credentials": {"nocoDbApi": "Your NocoDB Credential"}}, {"parameters": {"functionCode": "if (Array.isArray($json) && $json.length > 0) {\n  return [{ success: true, user: $json[0], plan: $json[0].plan }];\n}\nreturn [{ error: true, message: \"User not found\" }];"}, "id": "5", "name": "Format Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1220, 300]}], "connections": {"Webhook (Auth Entry)": {"main": [[{"node": "Extract Token", "type": "main", "index": 0}]]}, "Extract Token": {"main": [[{"node": "Verify JWT", "type": "main", "index": 0}]]}, "Verify JWT": {"main": [[{"node": "Lookup User (NocoDB)", "type": "main", "index": 0}]]}, "Lookup User (NocoDB)": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}}, "active": false}, {"name": "Middleware Gateway Workflow (Extended)", "nodes": [{"parameters": {"path": "api/:endpoint", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (API Gateway)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"workflowId": "Auth & Access Workflow", "options": {}}, "id": "2", "name": "Run Auth Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\nreturn [{\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: $json[\"params\"]?.endpoint || \"\"\n}];"}, "id": "3", "name": "<PERSON> Result", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}, {"parameters": {"functionCode": "const routes = {\n  worker1: {\n    workflow: 'Pro Worker Example Workflow',\n    allowedPlans: ['pro', 'enterprise']\n  },\n  worker2: {\n    workflow: 'Free Worker Workflow',\n    allowedPlans: ['free', 'pro', 'enterprise']\n  },\n  adminOnly: {\n    workflow: 'Admin Workflow',\n    allowedPlans: ['enterprise']\n  }\n};\n\nconst ep = $json.endpoint;\nif (!routes[ep]) {\n  return [{ status: 404, message: 'Unknown endpoint' }];\n}\n\nconst route = routes[ep];\nif (!route.allowedPlans.includes($json.plan)) {\n  return [{ status: 403, message: `Your plan (${ $json.plan }) does not allow access to ${ep}` }];\n}\n\nreturn [{\n  workflow: route.workflow,\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: ep\n}];"}, "id": "4", "name": "Route & Access Control", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [980, 300]}, {"parameters": {"workflowId": "={{$json.workflow}}", "options": {}}, "id": "5", "name": "Run Worker Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1220, 300]}], "connections": {"Webhook (API Gateway)": {"main": [[{"node": "Run Auth Workflow", "type": "main", "index": 0}]]}, "Run Auth Workflow": {"main": [[{"node": "<PERSON> Result", "type": "main", "index": 0}]]}, "Check Auth Result": {"main": [[{"node": "Route & Access Control", "type": "main", "index": 0}]]}, "Route & Access Control": {"main": [[{"node": "Run Worker Workflow", "type": "main", "index": 0}]]}}, "active": false}, {"name": "Free Worker Workflow", "nodes": [{"parameters": {"path": "free-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Free Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello ${$json.user?.email || 'Guest'}!`,\n  plan: $json.plan,\n  note: \"This is a free-tier endpoint available to all plans.\"\n}];"}, "id": "2", "name": "Free Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Free Entry)": {"main": [[{"node": "Free Logic", "type": "main", "index": 0}]]}}, "active": false}, {"name": "Pro Worker Example Workflow", "nodes": [{"parameters": {"path": "worker-endpoint", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Worker Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"workflowId": "Auth & Access Workflow"}, "id": "2", "name": "Run Auth Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\nif ($json.plan !== 'pro' && $json.plan !== 'enterprise') {\n  return [{ status: 403, message: \"Upgrade to Pro to use this feature.\" }];\n}\nreturn [{ status: 200, message: \"Pro worker action completed\", user: $json.user }];"}, "id": "3", "name": "Check Access & Do Work", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}], "connections": {"Webhook (Worker Entry)": {"main": [[{"node": "Run Auth Workflow", "type": "main", "index": 0}]]}, "Run Auth Workflow": {"main": [[{"node": "Check Access & Do Work", "type": "main", "index": 0}]]}}, "active": false}, {"name": "Admin Workflow", "nodes": [{"parameters": {"path": "admin-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Admin Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello Admin ${$json.user?.email || ''}!`,\n  user: $json.user,\n  plan: $json.plan,\n  note: \"This is a restricted enterprise-only endpoint.\"\n}];"}, "id": "2", "name": "Admin Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Admin Entry)": {"main": [[{"node": "Admin Logic", "type": "main", "index": 0}]]}}, "active": false}]