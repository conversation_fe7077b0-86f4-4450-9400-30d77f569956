{"name": "Middleware Gateway Workflow (Extended)", "nodes": [{"parameters": {"path": "api/:endpoint", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (API Gateway)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"workflowId": "Auth & Access Workflow", "options": {}}, "id": "2", "name": "Run Auth Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\n\n// Pass auth info + endpoint to next node\nreturn [{\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: $json[\"params\"]?.endpoint || \"\"\n}];"}, "id": "3", "name": "<PERSON> Result", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}, {"parameters": {"functionCode": "/*\n Define access rules for each endpoint\n - endpoint: which worker to call\n - allowedPlans: which user plans can access\n*/\n\nconst routes = {\n  worker1: {\n    workflow: 'Worker Example Workflow',\n    allowedPlans: ['pro', 'enterprise']\n  },\n  worker2: {\n    workflow: 'Another Worker Workflow',\n    allowedPlans: ['free', 'pro', 'enterprise']\n  },\n  adminOnly: {\n    workflow: 'Admin Workflow',\n    allowedPlans: ['enterprise']\n  }\n};\n\nconst ep = $json.endpoint;\n\nif (!routes[ep]) {\n  return [{ status: 404, message: 'Unknown endpoint' }];\n}\n\nconst route = routes[ep];\n\nif (!route.allowedPlans.includes($json.plan)) {\n  return [{ status: 403, message: `Your plan (${ $json.plan }) does not allow access to ${ep}` }];\n}\n\nreturn [{\n  workflow: route.workflow,\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: ep\n}];"}, "id": "4", "name": "Route & Access Control", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [980, 300]}, {"parameters": {"workflowId": "={{$json.workflow}}", "options": {}}, "id": "5", "name": "Run Worker Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1220, 300]}], "connections": {"Webhook (API Gateway)": {"main": [[{"node": "Run Auth Workflow", "type": "main", "index": 0}]]}, "Run Auth Workflow": {"main": [[{"node": "<PERSON> Result", "type": "main", "index": 0}]]}, "Check Auth Result": {"main": [[{"node": "Route & Access Control", "type": "main", "index": 0}]]}, "Route & Access Control": {"main": [[{"node": "Run Worker Workflow", "type": "main", "index": 0}]]}}, "active": false}