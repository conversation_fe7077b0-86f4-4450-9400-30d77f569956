{"name": "Free Worker Workflow", "nodes": [{"parameters": {"path": "free-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Free Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello ${$json.user?.email || 'Guest'}!`,\n  plan: $json.plan,\n  note: \"This is a free-tier endpoint available to all plans.\"\n}];"}, "id": "2", "name": "Free Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Free Entry)": {"main": [[{"node": "Free Logic", "type": "main", "index": 0}]]}}, "active": false}