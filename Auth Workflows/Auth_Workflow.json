{"name": "Auth & Access Workflow", "nodes": [{"parameters": {"path": "auth-check", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Auth Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "const authHeader = $json[\"headers\"][\"authorization\"];\nif (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n  return [{ error: true, message: \"Missing or invalid Authorization header\" }];\n}\n\nreturn [{ token: authHeader.split(\" \")[1] }];"}, "id": "2", "name": "Extract Token", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "const jwt = require('jsonwebtoken');\ntry {\n  const decoded = jwt.verify($json[\"token\"], $env.SUPABASE_JWT_SECRET);\n  return [{ userId: decoded.sub, email: decoded.email }];\n} catch (err) {\n  return [{ error: true, message: \"Invalid token\" }];\n}"}, "id": "3", "name": "Verify JWT", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}, {"parameters": {"operation": "list", "table": "users", "options": {}, "filters": "{\"id\": \"={{$json[\\\"userId\\\"]}}\"}"}, "id": "4", "name": "Lookup User (NocoDB)", "type": "n8n-nodes-base.nocoDb", "typeVersion": 1, "position": [980, 300], "credentials": {"nocoDbApi": "Your NocoDB Credential"}}, {"parameters": {"functionCode": "if (Array.isArray($json) && $json.length > 0) {\n  return [{ success: true, user: $json[0], plan: $json[0].plan }];\n}\nreturn [{ error: true, message: \"User not found\" }];"}, "id": "5", "name": "Format Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1220, 300]}], "connections": {"Webhook (Auth Entry)": {"main": [[{"node": "Extract Token", "type": "main", "index": 0}]]}, "Extract Token": {"main": [[{"node": "Verify JWT", "type": "main", "index": 0}]]}, "Verify JWT": {"main": [[{"node": "Lookup User (NocoDB)", "type": "main", "index": 0}]]}, "Lookup User (NocoDB)": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}}, "active": false}