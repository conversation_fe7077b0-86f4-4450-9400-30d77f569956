{"name": "Middleware Gateway Workflow", "nodes": [{"parameters": {"path": "api/:endpoint", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (API Gateway)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"workflowId": "Auth & Access Workflow", "options": {}}, "id": "2", "name": "Run Auth Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [500, 300]}, {"parameters": {"functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\n\n// Pass auth info + endpoint to next node\nreturn [{\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: $json[\"params\"]?.endpoint || \"\"\n}];"}, "id": "3", "name": "<PERSON> Result", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 300]}, {"parameters": {"functionCode": "switch ($json.endpoint) {\n  case 'worker1':\n    return [{ workflow: 'Worker Example Workflow' }];\n  case 'worker2':\n    return [{ workflow: 'Another Worker Workflow' }];\n  default:\n    return [{ status: 404, message: 'Unknown endpoint' }];\n}"}, "id": "4", "name": "Route to Worker", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [980, 300]}, {"parameters": {"workflowId": "={{$json.workflow}}", "options": {}}, "id": "5", "name": "Run Worker Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1220, 300]}], "connections": {"Webhook (API Gateway)": {"main": [[{"node": "Run Auth Workflow", "type": "main", "index": 0}]]}, "Run Auth Workflow": {"main": [[{"node": "<PERSON> Result", "type": "main", "index": 0}]]}, "Check Auth Result": {"main": [[{"node": "Route to Worker", "type": "main", "index": 0}]]}, "Route to Worker": {"main": [[{"node": "Run Worker Workflow", "type": "main", "index": 0}]]}}, "active": false}