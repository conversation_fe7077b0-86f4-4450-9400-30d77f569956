-- Custom claims function for JWT tokens
-- This function will be called whenever a JWT is issued

CREATE OR REPLACE FUNCTION auth.get_custom_claims(user_id UUID)
RETURNS JSONB
LANGUAGE SQL
STABLE
AS $$
  SELECT COALESCE(
    jsonb_build_object(
      'role', profiles.role,
      'email_verified', auth.users.email_confirmed_at IS NOT NULL,
      'profile_complete', profiles.updated_at IS NOT NULL
    ),
    '{}'::jsonb
  )
  FROM auth.users
  LEFT JOIN public.profiles ON auth.users.id = profiles.id
  WHERE auth.users.id = user_id;
$$;

-- Alternative approach using a hook (if your Supabase version supports it)
-- Create this as a database webhook or edge function

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  claims JSONB;
  user_role TEXT;
BEGIN
  -- Get user role from profiles table
  SELECT role INTO user_role
  FROM public.profiles
  WHERE id = (event->>'user_id')::UUID;
  
  -- Build custom claims
  claims := jsonb_build_object(
    'role', COALESCE(user_role, 'user'),
    'aal', event->'claims'->>'aal',
    'amr', event->'claims'->>'amr',
    'aud', event->'claims'->>'aud',
    'exp', event->'claims'->>'exp',
    'iat', event->'claims'->>'iat',
    'iss', event->'claims'->>'iss',
    'sub', event->'claims'->>'sub',
    'email', event->'claims'->>'email'
  );

  RETURN claims;
END;
$$;
