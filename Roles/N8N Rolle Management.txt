// N8N Code Node for User Role Management

const SUPABASE_URL = "https://db.homelynx.qzz.io";
const SUPABASE_SERVICE_KEY = "eyJ..."; // Your service role key (not anon key)

// Function to assign role to user
async function assignUserRole(userId, role, email = null) {
  const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_SERVICE_KEY,
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    },
    body: JSON.stringify({
      id: userId,
      email: email,
      role: role
    })
  });
  
  if (!response.ok) {
    // If profile exists, try updating instead
    const updateResponse = await fetch(`${SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}`, {
      method: 'PATCH',
      headers: {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify({
        role: role,
        updated_at: new Date().toISOString()
      })
    });
    
    if (!updateResponse.ok) {
      throw new Error(`Failed to update role: ${await updateResponse.text()}`);
    }
    
    return await updateResponse.json();
  }
  
  return await response.json();
}

// Function to get user role
async function getUserRole(userId) {
  const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles?id=eq.${userId}&select=role,email`, {
    method: 'GET',
    headers: {
      'apikey': SUPABASE_SERVICE_KEY,
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get role: ${await response.text()}`);
  }
  
  const data = await response.json();
  return data.length > 0 ? data[0] : null;
}

// Main execution based on operation type
const operation = items[0].json.operation || 'get'; // 'get', 'set', 'verify'
const userId = items[0].json.user_id;
const newRole = items[0].json.role;
const userEmail = items[0].json.email;

try {
  switch (operation) {
    case 'set':
      if (!userId || !newRole) {
        throw new Error('user_id and role are required for set operation');
      }
      const setResult = await assignUserRole(userId, newRole, userEmail);
      return [{ json: { success: true, operation: 'set', data: setResult } }];
      
    case 'get':
      if (!userId) {
        throw new Error('user_id is required for get operation');
      }
      const getResult = await getUserRole(userId);
      return [{ json: { success: true, operation: 'get', data: getResult } }];
      
    case 'verify':
      // Extract token from authorization header
      const authHeader = items[0].json.headers?.authorization || 
                        items[0].json.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error('MISSING_OR_INVALID_TOKEN');
      }
      
      const token = authHeader.replace('Bearer ', '');
      
      // Decode JWT payload (basic decode without verification for role extraction)
      const payload = JSON.parse(
        Buffer.from(token.split('.')[1], 'base64url').toString()
      );
      
      // Get additional role info from database
      const roleInfo = await getUserRole(payload.sub);
      
      return [{
        json: {
          success: true,
          operation: 'verify',
          user_id: payload.sub,
          email: payload.email,
          database_role: roleInfo?.role || 'user',
          jwt_claims: payload
        }
      }];
      
    default:
      throw new Error('Invalid operation. Use: get, set, or verify');
  }
  
} catch (error) {
  return [{
    json: {
      success: false,
      error: error.message,
      operation: operation
    }
  }];
}
