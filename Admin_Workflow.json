{"name": "Admin Workflow", "nodes": [{"parameters": {"path": "admin-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Admin Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello Admin ${$json.user?.email || ''}!`,\n  user: $json.user,\n  plan: $json.plan,\n  note: \"This is a restricted enterprise-only endpoint.\"\n}];"}, "id": "2", "name": "Admin Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Admin Entry)": {"main": [[{"node": "Admin Logic", "type": "main", "index": 0}]]}}, "active": false}