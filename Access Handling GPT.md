Good question 👍 You're essentially designing **authorization and access control** across a SaaS stack that uses:

* **Frontend** (React)
* **n8n workflows as backend APIs**
* **Users & subscription packages (plans/tiers)**

Let’s break this down.

---

## 🔑 Principles for SaaS Authorization

1. **Authentication always at the edge (before workflows are executed)**
   You want to make sure only **valid, logged-in users** can hit your workflows.
   → This means **JWT / API key validation** must happen **before or at the start of every workflow**.

2. **Authorization (access levels, packages/tiers)** should be **centralized** and reusable.
   Otherwise, each workflow will end up with duplicated logic that’s hard to maintain.

3. **Frontend should never enforce security alone**.

   * The frontend may show/hide features, but the **backend must be the final enforcer**.
   * Example: A user can always craft a manual API request — only backend rules stop abuse.

---

## 🛠️ Implementation Options for n8n

### Option A: **Single Authorization Workflow (Recommended)**

* Have **one central “Auth & Access” workflow** in n8n.
* Each API workflow calls this at the beginning (via **Sub-Workflow Call** or **Webhook → Execute Workflow**).
* The Auth workflow does:

  1. Validate token (JWT from Supabase/Auth provider).
  2. Look up user + subscription/package in NocoDB (or Supabase if you store plans there).
  3. Return **OK with permissions/plan data** or **Error (401/403)**.

✅ Pros: Single source of truth, easy to update.
❌ Con: Slight overhead of sub-calls (but worth it for maintainability).

---

### Option B: **Inline Auth in Each Workflow**

* Add a “JWT Verify + DB Lookup” step at the start of every workflow.
* Each workflow enforces its own tier/feature limits.

✅ Pros: No extra calls.
❌ Con: Hard to maintain, risk of inconsistencies.

---

### Option C: **Hybrid (Auth Middleware + Feature Limits Inline)**

* Central workflow for **authentication + user plan lookup**.
* Workflows themselves only enforce **package-specific rules** (like “Free plan max 5 jobs”).

✅ Best for SaaS where **packages unlock different features**.

---

## 🔧 Practical Setup

1. **Frontend**

   * User logs in via Supabase/Auth provider → gets JWT.
   * Send JWT in `Authorization: Bearer <token>` header to workflows.

2. **n8n Workflows**

   * **Webhook Node (entrypoint)** → calls **Auth Workflow**.
   * If authorized → continue.
   * If not → return 401/403 JSON response.

3. **Access Control**

   * Auth workflow attaches user info + plan to execution data (`$json.user` = {id, email, plan, limits}).
   * Worker workflows read from that instead of re-querying.

---

## 🚀 Recommendation for You

Given you have **multiple workflows acting as APIs**, the cleanest approach is:

* **One central `Auth & Access` workflow** (validates JWT + retrieves plan/limits).
* Worker workflows **always call this first**.
* Worker workflows enforce **feature-specific limits** (e.g., “Pro plan gets 10 jobs/day”).

That way, all workflows share a single logic for **who is the user and what package they’re on**, while still allowing flexibility in enforcement.

---

Perfect — let’s build this step by step.

I’ll give you two workflows in **n8n’s `workflow.json` export format**:

1. **Auth & Access Workflow** (`Auth_Workflow.json`)

   * Input: JWT (in `Authorization: Bearer <token>`)
   * Verifies JWT with your Supabase secret
   * Looks up the user + plan from NocoDB (you can adjust this node depending on your database)
   * Returns `{ user, plan }` if valid or an error response

2. **Worker Workflow** (`Worker_Workflow.json`)

   * Starts with a Webhook
   * Calls the **Auth workflow** as a subworkflow
   * Uses the returned `user` + `plan` info to enforce rules

---

## 1. Auth & Access Workflow (Auth\_Workflow\.json)

```json
{
  "name": "Auth & Access Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "auth-check",
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "1",
      "name": "Webhook (Auth Entry)",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "functionCode": "const authHeader = $json[\"headers\"][\"authorization\"];\nif (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n  return [{ error: true, message: \"Missing or invalid Authorization header\" }];\n}\n\nreturn [{ token: authHeader.split(\" \")[1] }];"
      },
      "id": "2",
      "name": "Extract Token",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "functionCode": "const jwt = require('jsonwebtoken');\ntry {\n  const decoded = jwt.verify($json[\"token\"], $env.SUPABASE_JWT_SECRET);\n  return [{ userId: decoded.sub, email: decoded.email }];\n} catch (err) {\n  return [{ error: true, message: \"Invalid token\" }];\n}"
      },
      "id": "3",
      "name": "Verify JWT",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [740, 300]
    },
    {
      "parameters": {
        "operation": "list",
        "table": "users",
        "options": {},
        "filters": "{\"id\": \"={{$json[\\\"userId\\\"]}}\"}"
      },
      "id": "4",
      "name": "Lookup User (NocoDB)",
      "type": "n8n-nodes-base.nocoDb",
      "typeVersion": 1,
      "position": [980, 300],
      "credentials": {
        "nocoDbApi": "Your NocoDB Credential"
      }
    },
    {
      "parameters": {
        "functionCode": "if (Array.isArray($json) && $json.length > 0) {\n  return [{ success: true, user: $json[0], plan: $json[0].plan }];\n}\nreturn [{ error: true, message: \"User not found\" }];"
      },
      "id": "5",
      "name": "Format Response",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [1220, 300]
    }
  ],
  "connections": {
    "Webhook (Auth Entry)": { "main": [[{ "node": "Extract Token", "type": "main", "index": 0 }]] },
    "Extract Token": { "main": [[{ "node": "Verify JWT", "type": "main", "index": 0 }]] },
    "Verify JWT": { "main": [[{ "node": "Lookup User (NocoDB)", "type": "main", "index": 0 }]] },
    "Lookup User (NocoDB)": { "main": [[{ "node": "Format Response", "type": "main", "index": 0 }]] }
  },
  "active": false
}
```

---

## 2. Worker Workflow (Worker\_Workflow\.json)

```json
{
  "name": "Worker Example Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "worker-endpoint",
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "1",
      "name": "Webhook (Worker Entry)",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "workflowId": "Auth & Access Workflow"
      },
      "id": "2",
      "name": "Run Auth Workflow",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\n\n// Example: enforce only PRO users\nif ($json.plan !== 'pro') {\n  return [{ status: 403, message: \"Upgrade to Pro to use this feature.\" }];\n}\n\nreturn [{ status: 200, message: \"Worker action completed\", user: $json.user }];"
      },
      "id": "3",
      "name": "Check Access & Do Work",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [740, 300]
    }
  ],
  "connections": {
    "Webhook (Worker Entry)": { "main": [[{ "node": "Run Auth Workflow", "type": "main", "index": 0 }]] },
    "Run Auth Workflow": { "main": [[{ "node": "Check Access & Do Work", "type": "main", "index": 0 }]] }
  },
  "active": false
}
```

---

## 🔧 Notes

* Replace `Your NocoDB Credential` with the credential name you configured in n8n.
* `SUPABASE_JWT_SECRET` must be set as an environment variable in your n8n instance.
* The **Worker workflow** enforces **Pro plan only** as an example. You can extend this with per-package rules.
* Import both JSONs in **n8n → Workflows → Import from File**.

---

👉 Do you want me to also create a **third workflow** that acts like a **"middleware gateway"** (so all requests pass through it, then it calls worker workflows), instead of each worker calling the auth workflow directly?

Got it 👍
We’ll add a **Middleware Gateway Workflow** that acts like a router in front of your API:

* **Receives all API calls via a single Webhook** (`/api/:endpoint`).
* **Calls the Auth Workflow** first.
* **Routes the request** to the correct Worker Workflow based on `endpoint` (from the URL path or query).
* Responds with either an error or the worker’s response.

---

## 3. Middleware Gateway Workflow (Gateway\_Workflow\.json)

```json
{
  "name": "Middleware Gateway Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "api/:endpoint",
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "1",
      "name": "Webhook (API Gateway)",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "workflowId": "Auth & Access Workflow",
        "options": {}
      },
      "id": "2",
      "name": "Run Auth Workflow",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\n\n// Pass auth info + endpoint to next node\nreturn [{\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: $json[\"params\"]?.endpoint || \"\"\n}];"
      },
      "id": "3",
      "name": "Check Auth Result",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [740, 300]
    },
    {
      "parameters": {
        "functionCode": "switch ($json.endpoint) {\n  case 'worker1':\n    return [{ workflow: 'Worker Example Workflow' }];\n  case 'worker2':\n    return [{ workflow: 'Another Worker Workflow' }];\n  default:\n    return [{ status: 404, message: 'Unknown endpoint' }];\n}"
      },
      "id": "4",
      "name": "Route to Worker",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [980, 300]
    },
    {
      "parameters": {
        "workflowId": "={{$json.workflow}}",
        "options": {}
      },
      "id": "5",
      "name": "Run Worker Workflow",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 1,
      "position": [1220, 300]
    }
  ],
  "connections": {
    "Webhook (API Gateway)": {
      "main": [
        [{ "node": "Run Auth Workflow", "type": "main", "index": 0 }]
      ]
    },
    "Run Auth Workflow": {
      "main": [
        [{ "node": "Check Auth Result", "type": "main", "index": 0 }]
      ]
    },
    "Check Auth Result": {
      "main": [
        [{ "node": "Route to Worker", "type": "main", "index": 0 }]
      ]
    },
    "Route to Worker": {
      "main": [
        [{ "node": "Run Worker Workflow", "type": "main", "index": 0 }]
      ]
    }
  },
  "active": false
}
```

---

## 🔧 How It Works

* Frontend calls → `https://your-n8n/api/worker1`
* Gateway Webhook extracts `endpoint=worker1`
* Gateway calls **Auth Workflow** → verifies JWT + retrieves user & plan
* Gateway checks authorization → passes control
* Gateway routes request to the correct Worker Workflow (`Worker Example Workflow`, `Another Worker Workflow`, etc.)

---

✅ **Pros**:

* Centralized entrypoint → you can set **rate limits, logging, analytics** here.
* Each Worker Workflow no longer needs to worry about authentication.
* Easy to add more workers by extending the `Route to Worker` function.

❌ **Cons**:

* Gateway adds a small overhead hop.

---

👉 Do you want me to extend this **Gateway Workflow** so it can also handle **role-based access rules (per endpoint)** directly inside the router (instead of inside the workers)?

Perfect ✅
Let’s extend the **Middleware Gateway Workflow** so it doesn’t just forward requests but also **applies role- or plan-based rules per endpoint**.

This way, you define in one place:

* Which **endpoints exist**
* Which **plans (Free, Pro, Enterprise, etc.)** can access them
* Then it either calls the worker workflow or immediately returns a **403**

---

## Extended Middleware Gateway Workflow (Gateway\_Workflow\_Extended.json)

```json
{
  "name": "Middleware Gateway Workflow (Extended)",
  "nodes": [
    {
      "parameters": {
        "path": "api/:endpoint",
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "1",
      "name": "Webhook (API Gateway)",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "workflowId": "Auth & Access Workflow",
        "options": {}
      },
      "id": "2",
      "name": "Run Auth Workflow",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 1,
      "position": [500, 300]
    },
    {
      "parameters": {
        "functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\n\n// Pass auth info + endpoint to next node\nreturn [{\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: $json[\"params\"]?.endpoint || \"\"\n}];"
      },
      "id": "3",
      "name": "Check Auth Result",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [740, 300]
    },
    {
      "parameters": {
        "functionCode": "/*\n Define access rules for each endpoint\n - endpoint: which worker to call\n - allowedPlans: which user plans can access\n*/\n\nconst routes = {\n  worker1: {\n    workflow: 'Worker Example Workflow',\n    allowedPlans: ['pro', 'enterprise']\n  },\n  worker2: {\n    workflow: 'Another Worker Workflow',\n    allowedPlans: ['free', 'pro', 'enterprise']\n  },\n  adminOnly: {\n    workflow: 'Admin Workflow',\n    allowedPlans: ['enterprise']\n  }\n};\n\nconst ep = $json.endpoint;\n\nif (!routes[ep]) {\n  return [{ status: 404, message: 'Unknown endpoint' }];\n}\n\nconst route = routes[ep];\n\nif (!route.allowedPlans.includes($json.plan)) {\n  return [{ status: 403, message: `Your plan (${ $json.plan }) does not allow access to ${ep}` }];\n}\n\nreturn [{\n  workflow: route.workflow,\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: ep\n}];"
      },
      "id": "4",
      "name": "Route & Access Control",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [980, 300]
    },
    {
      "parameters": {
        "workflowId": "={{$json.workflow}}",
        "options": {}
      },
      "id": "5",
      "name": "Run Worker Workflow",
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 1,
      "position": [1220, 300]
    }
  ],
  "connections": {
    "Webhook (API Gateway)": {
      "main": [
        [{ "node": "Run Auth Workflow", "type": "main", "index": 0 }]
      ]
    },
    "Run Auth Workflow": {
      "main": [
        [{ "node": "Check Auth Result", "type": "main", "index": 0 }]
      ]
    },
    "Check Auth Result": {
      "main": [
        [{ "node": "Route & Access Control", "type": "main", "index": 0 }]
      ]
    },
    "Route & Access Control": {
      "main": [
        [{ "node": "Run Worker Workflow", "type": "main", "index": 0 }]
      ]
    }
  },
  "active": false
}
```

---

## 🔧 How It Works Now

1. Frontend calls → `https://your-n8n/api/worker1`
2. Gateway webhook receives → extracts `endpoint = worker1`
3. Calls **Auth Workflow** → validates JWT & gets `{user, plan}`
4. `Route & Access Control` node checks rules:

   * `worker1` → only `pro`, `enterprise`
   * `worker2` → `free`, `pro`, `enterprise`
   * `adminOnly` → only `enterprise`
5. If allowed → executes the mapped Worker Workflow.
6. If denied → returns `403`.

---

## ✅ Benefits

* **Single entrypoint** for *all APIs*.
* **Central access matrix** in one place (`routes` object).
* Easy to extend: just add `{ workflow, allowedPlans }` for new endpoints.
* Workers don’t need to know about roles — they just do the work.

---

👉 Do you also want me to make an **example “Admin Workflow”** (restricted to `enterprise` plan) so you have a third worker to test the rules?

Perfect 👍
Here’s an **Admin Workflow** you can import into n8n.
It will only run if the **Gateway Workflow** routes to it and the user has an **enterprise plan**.

---

## 4. Admin Workflow (Admin\_Workflow\.json)

```json
{
  "name": "Admin Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "admin-test",
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "1",
      "name": "Webhook (Admin Entry)",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "functionCode": "return [{\n  status: 200,\n  message: `Hello Admin ${$json.user?.email || ''}!`,\n  user: $json.user,\n  plan: $json.plan,\n  note: \"This is a restricted enterprise-only endpoint.\"\n}];"
      },
      "id": "2",
      "name": "Admin Logic",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [500, 300]
    }
  ],
  "connections": {
    "Webhook (Admin Entry)": {
      "main": [
        [{ "node": "Admin Logic", "type": "main", "index": 0 }]
      ]
    }
  },
  "active": false
}
```

---

## 🔧 How It Fits Together

1. **Frontend/API call:** `https://your-n8n/api/adminOnly`

2. **Gateway Workflow** receives it:

   * Runs **Auth Workflow** → gets `{ user, plan }`
   * Checks routing rules → `adminOnly` requires `"enterprise"`
   * If allowed → forwards to **Admin Workflow**
   * If not allowed → returns `403`

3. **Admin Workflow** executes its logic and returns a message like:

```json
{
  "status": 200,
  "message": "<NAME_EMAIL>!",
  "plan": "enterprise",
  "note": "This is a restricted enterprise-only endpoint."
}
```

---

✅ Now you have a **full chain**:

* **Auth Workflow** (JWT + user lookup)
* **Gateway Workflow (Extended)** (routes + plan-based access)
* **Worker Example Workflow** (Pro feature)
* **Admin Workflow** (Enterprise feature)

---

👉 Do you want me to also prepare a **demo “Free Worker Workflow”** (for free-tier users) so you can test the full matrix (`free`, `pro`, `enterprise`)?

Great 🚀 — let’s complete the matrix with a **Free Worker Workflow**.
This will be accessible to `free`, `pro`, and `enterprise` users (as defined in the Gateway Workflow’s access rules).

---

## 5. Free Worker Workflow (Free\_Workflow\.json)

```json
{
  "name": "Free Worker Workflow",
  "nodes": [
    {
      "parameters": {
        "path": "free-test",
        "responseMode": "lastNode",
        "options": {}
      },
      "id": "1",
      "name": "Webhook (Free Entry)",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "functionCode": "return [{\n  status: 200,\n  message: `Hello ${$json.user?.email || 'Guest'}!`,\n  plan: $json.plan,\n  note: \"This is a free-tier endpoint available to all plans.\"\n}];"
      },
      "id": "2",
      "name": "Free Logic",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [500, 300]
    }
  ],
  "connections": {
    "Webhook (Free Entry)": {
      "main": [
        [{ "node": "Free Logic", "type": "main", "index": 0 }]
      ]
    }
  },
  "active": false
}
```

---

## 🔧 How It Fits With Gateway

1. **Frontend/API call:**

   ```
   https://your-n8n/api/worker2
   ```

2. **Gateway Workflow (Extended)** rules:

   ```js
   worker2: {
     workflow: 'Free Worker Workflow',
     allowedPlans: ['free', 'pro', 'enterprise']
   }
   ```

3. Flow:

   * Gateway runs **Auth Workflow** → gets `{ user, plan }`
   * Router checks → all plans are allowed
   * Calls **Free Worker Workflow**
   * Workflow responds with:

```json
{
  "status": 200,
  "message": "Hello <EMAIL>!",
  "plan": "free",
  "note": "This is a free-tier endpoint available to all plans."
}
```

---

✅ Now you have the **full 3-tier example**:

* **Free Worker Workflow** → available to all
* **Worker Example Workflow** → Pro+
* **Admin Workflow** → Enterprise only

---

👉 Would you like me to also bundle all **4 workflows** (`Auth`, `Gateway`, `Free`, `Pro`, `Admin`) into a **single JSON export file** so you can import them in one shot into n8n?

Perfect 👍 — I’ll bundle all **4 workflows** into a single JSON export file you can import directly into n8n.

This file will include:

1. **Auth & Access Workflow**
2. **Middleware Gateway Workflow (Extended)**
3. **Free Worker Workflow**
4. **Pro Worker Example Workflow**
5. **Admin Workflow**

---

## Full Export (All\_Workflows.json)

```json
[
  {
    "name": "Auth & Access Workflow",
    "nodes": [
      {
        "parameters": {
          "path": "auth-check",
          "responseMode": "lastNode",
          "options": {}
        },
        "id": "1",
        "name": "Webhook (Auth Entry)",
        "type": "n8n-nodes-base.webhook",
        "typeVersion": 1,
        "position": [240, 300]
      },
      {
        "parameters": {
          "functionCode": "const authHeader = $json[\"headers\"][\"authorization\"];\nif (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n  return [{ error: true, message: \"Missing or invalid Authorization header\" }];\n}\n\nreturn [{ token: authHeader.split(\" \")[1] }];"
        },
        "id": "2",
        "name": "Extract Token",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [500, 300]
      },
      {
        "parameters": {
          "functionCode": "const jwt = require('jsonwebtoken');\ntry {\n  const decoded = jwt.verify($json[\"token\"], $env.SUPABASE_JWT_SECRET);\n  return [{ userId: decoded.sub, email: decoded.email }];\n} catch (err) {\n  return [{ error: true, message: \"Invalid token\" }];\n}"
        },
        "id": "3",
        "name": "Verify JWT",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [740, 300]
      },
      {
        "parameters": {
          "operation": "list",
          "table": "users",
          "options": {},
          "filters": "{\"id\": \"={{$json[\\\"userId\\\"]}}\"}"
        },
        "id": "4",
        "name": "Lookup User (NocoDB)",
        "type": "n8n-nodes-base.nocoDb",
        "typeVersion": 1,
        "position": [980, 300],
        "credentials": {
          "nocoDbApi": "Your NocoDB Credential"
        }
      },
      {
        "parameters": {
          "functionCode": "if (Array.isArray($json) && $json.length > 0) {\n  return [{ success: true, user: $json[0], plan: $json[0].plan }];\n}\nreturn [{ error: true, message: \"User not found\" }];"
        },
        "id": "5",
        "name": "Format Response",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [1220, 300]
      }
    ],
    "connections": {
      "Webhook (Auth Entry)": { "main": [[{ "node": "Extract Token", "type": "main", "index": 0 }]] },
      "Extract Token": { "main": [[{ "node": "Verify JWT", "type": "main", "index": 0 }]] },
      "Verify JWT": { "main": [[{ "node": "Lookup User (NocoDB)", "type": "main", "index": 0 }]] },
      "Lookup User (NocoDB)": { "main": [[{ "node": "Format Response", "type": "main", "index": 0 }]] }
    },
    "active": false
  },
  {
    "name": "Middleware Gateway Workflow (Extended)",
    "nodes": [
      {
        "parameters": {
          "path": "api/:endpoint",
          "responseMode": "lastNode",
          "options": {}
        },
        "id": "1",
        "name": "Webhook (API Gateway)",
        "type": "n8n-nodes-base.webhook",
        "typeVersion": 1,
        "position": [240, 300]
      },
      {
        "parameters": {
          "workflowId": "Auth & Access Workflow",
          "options": {}
        },
        "id": "2",
        "name": "Run Auth Workflow",
        "type": "n8n-nodes-base.executeWorkflow",
        "typeVersion": 1,
        "position": [500, 300]
      },
      {
        "parameters": {
          "functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\nreturn [{\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: $json[\"params\"]?.endpoint || \"\"\n}];"
        },
        "id": "3",
        "name": "Check Auth Result",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [740, 300]
      },
      {
        "parameters": {
          "functionCode": "const routes = {\n  worker1: {\n    workflow: 'Pro Worker Example Workflow',\n    allowedPlans: ['pro', 'enterprise']\n  },\n  worker2: {\n    workflow: 'Free Worker Workflow',\n    allowedPlans: ['free', 'pro', 'enterprise']\n  },\n  adminOnly: {\n    workflow: 'Admin Workflow',\n    allowedPlans: ['enterprise']\n  }\n};\n\nconst ep = $json.endpoint;\nif (!routes[ep]) {\n  return [{ status: 404, message: 'Unknown endpoint' }];\n}\n\nconst route = routes[ep];\nif (!route.allowedPlans.includes($json.plan)) {\n  return [{ status: 403, message: `Your plan (${ $json.plan }) does not allow access to ${ep}` }];\n}\n\nreturn [{\n  workflow: route.workflow,\n  user: $json.user,\n  plan: $json.plan,\n  endpoint: ep\n}];"
        },
        "id": "4",
        "name": "Route & Access Control",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [980, 300]
      },
      {
        "parameters": {
          "workflowId": "={{$json.workflow}}",
          "options": {}
        },
        "id": "5",
        "name": "Run Worker Workflow",
        "type": "n8n-nodes-base.executeWorkflow",
        "typeVersion": 1,
        "position": [1220, 300]
      }
    ],
    "connections": {
      "Webhook (API Gateway)": { "main": [[{ "node": "Run Auth Workflow", "type": "main", "index": 0 }]] },
      "Run Auth Workflow": { "main": [[{ "node": "Check Auth Result", "type": "main", "index": 0 }]] },
      "Check Auth Result": { "main": [[{ "node": "Route & Access Control", "type": "main", "index": 0 }]] },
      "Route & Access Control": { "main": [[{ "node": "Run Worker Workflow", "type": "main", "index": 0 }]] }
    },
    "active": false
  },
  {
    "name": "Free Worker Workflow",
    "nodes": [
      {
        "parameters": {
          "path": "free-test",
          "responseMode": "lastNode",
          "options": {}
        },
        "id": "1",
        "name": "Webhook (Free Entry)",
        "type": "n8n-nodes-base.webhook",
        "typeVersion": 1,
        "position": [240, 300]
      },
      {
        "parameters": {
          "functionCode": "return [{\n  status: 200,\n  message: `Hello ${$json.user?.email || 'Guest'}!`,\n  plan: $json.plan,\n  note: \"This is a free-tier endpoint available to all plans.\"\n}];"
        },
        "id": "2",
        "name": "Free Logic",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [500, 300]
      }
    ],
    "connections": {
      "Webhook (Free Entry)": { "main": [[{ "node": "Free Logic", "type": "main", "index": 0 }]] }
    },
    "active": false
  },
  {
    "name": "Pro Worker Example Workflow",
    "nodes": [
      {
        "parameters": {
          "path": "worker-endpoint",
          "responseMode": "lastNode",
          "options": {}
        },
        "id": "1",
        "name": "Webhook (Worker Entry)",
        "type": "n8n-nodes-base.webhook",
        "typeVersion": 1,
        "position": [240, 300]
      },
      {
        "parameters": {
          "workflowId": "Auth & Access Workflow"
        },
        "id": "2",
        "name": "Run Auth Workflow",
        "type": "n8n-nodes-base.executeWorkflow",
        "typeVersion": 1,
        "position": [500, 300]
      },
      {
        "parameters": {
          "functionCode": "if ($json.error) {\n  return [{ status: 403, message: $json.message }];\n}\nif ($json.plan !== 'pro' && $json.plan !== 'enterprise') {\n  return [{ status: 403, message: \"Upgrade to Pro to use this feature.\" }];\n}\nreturn [{ status: 200, message: \"Pro worker action completed\", user: $json.user }];"
        },
        "id": "3",
        "name": "Check Access & Do Work",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [740, 300]
      }
    ],
    "connections": {
      "Webhook (Worker Entry)": { "main": [[{ "node": "Run Auth Workflow", "type": "main", "index": 0 }]] },
      "Run Auth Workflow": { "main": [[{ "node": "Check Access & Do Work", "type": "main", "index": 0 }]] }
    },
    "active": false
  },
  {
    "name": "Admin Workflow",
    "nodes": [
      {
        "parameters": {
          "path": "admin-test",
          "responseMode": "lastNode",
          "options": {}
        },
        "id": "1",
        "name": "Webhook (Admin Entry)",
        "type": "n8n-nodes-base.webhook",
        "typeVersion": 1,
        "position": [240, 300]
      },
      {
        "parameters": {
          "functionCode": "return [{\n  status: 200,\n  message: `Hello Admin ${$json.user?.email || ''}!`,\n  user: $json.user,\n  plan: $json.plan,\n  note: \"This is a restricted enterprise-only endpoint.\"\n}];"
        },
        "id": "2",
        "name": "Admin Logic",
        "type": "n8n-nodes-base.function",
        "typeVersion": 1,
        "position": [500, 300]
      }
    ],
    "connections": {
      "Webhook (Admin Entry)": { "main": [[{ "node": "Admin Logic", "type": "main", "index": 0 }]] }
    },
    "active": false
  }
]
```

---

## 🚀 How to Use

1. Save this JSON as `All_Workflows.json`.
2. In n8n → **Workflows → Import from File** → select it.
3. It will create **5 workflows** at once.
4. Make sure to:

   * Add your **NocoDB credentials** in the Auth workflow.
   * Set `SUPABASE_JWT_SECRET` in n8n’s environment.

---

👉 Do you also want me to give you a **sample JWT** + minimal **Supabase/NocoDB user table structure** so you can test these workflows right away?

Perfect 🙌 Here’s a **single JSON export** containing all **4 workflows**:

* **Auth Workflow** (validates JWT and returns `{ user, plan }`)
* **Gateway Workflow** (middleware router + access control)
* **Free Worker Workflow** (accessible to all plans)
* **Pro Worker Workflow** (Pro+ only)
* **Admin Workflow** (Enterprise only)

---

## 🔽 Full Export (Workflows.json)

```json
{
  "meta": {
    "instanceId": "bundle-demo-001"
  },
  "workflows": [
    {
      "id": "1",
      "name": "Auth Workflow",
      "nodes": [
        {
          "parameters": {
            "path": "auth-check",
            "responseMode": "lastNode",
            "options": {}
          },
          "id": "1",
          "name": "Webhook (Auth Entry)",
          "type": "n8n-nodes-base.webhook",
          "typeVersion": 1,
          "position": [240, 300]
        },
        {
          "parameters": {
            "functionCode": "const authHeader = $json.headers?.authorization;\nif (!authHeader || !authHeader.startsWith('Bearer ')) {\n  return [{ status: 401, error: 'Missing or invalid Authorization header' }];\n}\n\nconst token = authHeader.split(' ')[1];\n\n// Demo decoding (replace with real JWT verification)\nlet user;\nif (token === 'free-token') user = { email: '<EMAIL>', plan: 'free' };\nelse if (token === 'pro-token') user = { email: '<EMAIL>', plan: 'pro' };\nelse if (token === 'admin-token') user = { email: '<EMAIL>', plan: 'enterprise' };\nelse return [{ status: 403, error: 'Invalid token' }];\n\nreturn [{ status: 200, user, plan: user.plan }];"
          },
          "id": "2",
          "name": "Validate Token",
          "type": "n8n-nodes-base.function",
          "typeVersion": 1,
          "position": [500, 300]
        }
      ],
      "connections": {
        "Webhook (Auth Entry)": {
          "main": [
            [{ "node": "Validate Token", "type": "main", "index": 0 }]
          ]
        }
      },
      "active": false
    },
    {
      "id": "2",
      "name": "Gateway Workflow",
      "nodes": [
        {
          "parameters": {
            "path": "api/:endpoint",
            "responseMode": "lastNode",
            "options": {}
          },
          "id": "1",
          "name": "Webhook (Gateway Entry)",
          "type": "n8n-nodes-base.webhook",
          "typeVersion": 1,
          "position": [240, 300]
        },
        {
          "parameters": {
            "functionCode": "const endpoint = $json.endpoint;\nconst headers = $json.headers;\n\nreturn [{ endpoint, headers }];"
          },
          "id": "2",
          "name": "Extract Request",
          "type": "n8n-nodes-base.function",
          "typeVersion": 1,
          "position": [480, 300]
        },
        {
          "parameters": {
            "authentication": "none",
            "url": "http://localhost:5678/webhook/auth-check",
            "options": {}
          },
          "id": "3",
          "name": "Call Auth Workflow",
          "type": "n8n-nodes-base.httpRequest",
          "typeVersion": 1,
          "position": [720, 300]
        },
        {
          "parameters": {
            "functionCode": "const auth = items[0].json;\nif (auth.status !== 200) {\n  return [{ status: auth.status, error: auth.error }];\n}\n\nconst endpoint = $json.endpoint;\n\n// Access rules\nconst routes = {\n  worker1: { workflow: 'Pro Worker Workflow', allowedPlans: ['pro', 'enterprise'] },\n  worker2: { workflow: 'Free Worker Workflow', allowedPlans: ['free','pro','enterprise'] },\n  adminOnly: { workflow: 'Admin Workflow', allowedPlans: ['enterprise'] }\n};\n\nconst route = routes[endpoint];\nif (!route) return [{ status: 404, error: 'Unknown endpoint' }];\n\nif (!route.allowedPlans.includes(auth.plan)) {\n  return [{ status: 403, error: 'Forbidden: plan not allowed' }];\n}\n\nreturn [{\n  status: 200,\n  nextWorkflow: route.workflow,\n  user: auth.user,\n  plan: auth.plan,\n  endpoint\n}];"
          },
          "id": "4",
          "name": "Router",
          "type": "n8n-nodes-base.function",
          "typeVersion": 1,
          "position": [960, 300]
        },
        {
          "parameters": {
            "workflow": "={{$json[\"nextWorkflow\"]}}",
            "options": {}
          },
          "id": "5",
          "name": "Execute Workflow",
          "type": "n8n-nodes-base.executeWorkflow",
          "typeVersion": 1,
          "position": [1200, 300]
        }
      ],
      "connections": {
        "Webhook (Gateway Entry)": {
          "main": [
            [{ "node": "Extract Request", "type": "main", "index": 0 }]
          ]
        },
        "Extract Request": {
          "main": [
            [{ "node": "Call Auth Workflow", "type": "main", "index": 0 }]
          ]
        },
        "Call Auth Workflow": {
          "main": [
            [{ "node": "Router", "type": "main", "index": 0 }]
          ]
        },
        "Router": {
          "main": [
            [{ "node": "Execute Workflow", "type": "main", "index": 0 }]
          ]
        }
      },
      "active": false
    },
    {
      "id": "3",
      "name": "Free Worker Workflow",
      "nodes": [
        {
          "parameters": {
            "path": "free-test",
            "responseMode": "lastNode",
            "options": {}
          },
          "id": "1",
          "name": "Webhook (Free Entry)",
          "type": "n8n-nodes-base.webhook",
          "typeVersion": 1,
          "position": [240, 300]
        },
        {
          "parameters": {
            "functionCode": "return [{\n  status: 200,\n  message: `Hello ${$json.user?.email || 'Guest'}!`,\n  plan: $json.plan,\n  note: \"This is a free-tier endpoint available to all plans.\"\n}];"
          },
          "id": "2",
          "name": "Free Logic",
          "type": "n8n-nodes-base.function",
          "typeVersion": 1,
          "position": [500, 300]
        }
      ],
      "connections": {
        "Webhook (Free Entry)": {
          "main": [
            [{ "node": "Free Logic", "type": "main", "index": 0 }]
          ]
        }
      },
      "active": false
    },
    {
      "id": "4",
      "name": "Pro Worker Workflow",
      "nodes": [
        {
          "parameters": {
            "path": "pro-test",
            "responseMode": "lastNode",
            "options": {}
          },
          "id": "1",
          "name": "Webhook (Pro Entry)",
          "type": "n8n-nodes-base.webhook",
          "typeVersion": 1,
          "position": [240, 300]
        },
        {
          "parameters": {
            "functionCode": "return [{\n  status: 200,\n  message: `Hello Pro user ${$json.user?.email || ''}!`,\n  plan: $json.plan,\n  note: \"This is a Pro feature only endpoint.\"\n}];"
          },
          "id": "2",
          "name": "Pro Logic",
          "type": "n8n-nodes-base.function",
          "typeVersion": 1,
          "position": [500, 300]
        }
      ],
      "connections": {
        "Webhook (Pro Entry)": {
          "main": [
            [{ "node": "Pro Logic", "type": "main", "index": 0 }]
          ]
        }
      },
      "active": false
    },
    {
      "id": "5",
      "name": "Admin Workflow",
      "nodes": [
        {
          "parameters": {
            "path": "admin-test",
            "responseMode": "lastNode",
            "options": {}
          },
          "id": "1",
          "name": "Webhook (Admin Entry)",
          "type": "n8n-nodes-base.webhook",
          "typeVersion": 1,
          "position": [240, 300]
        },
        {
          "parameters": {
            "functionCode": "return [{\n  status: 200,\n  message: `Hello Admin ${$json.user?.email || ''}!`,\n  user: $json.user,\n  plan: $json.plan,\n  note: \"This is a restricted enterprise-only endpoint.\"\n}];"
          },
          "id": "2",
          "name": "Admin Logic",
          "type": "n8n-nodes-base.function",
          "typeVersion": 1,
          "position": [500, 300]
        }
      ],
      "connections": {
        "Webhook (Admin Entry)": {
          "main": [
            [{ "node": "Admin Logic", "type": "main", "index": 0 }]
          ]
        }
      },
      "active": false
    }
  ]
}
```

---

## 🔧 How to Use

1. Save the above as `Workflows.json`.
2. In **n8n → Workflows → Import from File**, select it.
3. You’ll now have all **5 workflows** in one go.
4. Test with curl:

```bash
# Free user
curl -H "Authorization: Bearer free-token" http://localhost:5678/webhook/api/worker2

# Pro user
curl -H "Authorization: Bearer pro-token" http://localhost:5678/webhook/api/worker1

# Admin user
curl -H "Authorization: Bearer admin-token" http://localhost:5678/webhook/api/adminOnly
```

---

Do you want me to also add a **dummy database lookup** (instead of static tokens) inside the **Auth Workflow** so it feels closer to production?


Perfect — let’s build something you can actually **plug into Supabase** and test with your n8n workflows.

---

# 🔑 Sample JWT (Supabase-style)

Supabase issues JWTs signed with your **JWT secret** (default: `SUPABASE_JWT_SECRET` in your project settings).
Here’s a **sample JWT payload** (base64 decoded) you can use for testing:

```json
{
  "sub": "c6f6f8b0-1fcd-4a24-bf4a-12d4a4e2f2d8",
  "email": "<EMAIL>",
  "role": "authenticated",
  "exp": 1755600000, 
  "user_metadata": {
    "plan": "pro"
  }
}
```

### Example Encoded JWT (header.payload.signature)

```jwt
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************.l62Xe5Q4IBctSu2b1d7q9i8Y8sNYzofp61X2wFrGVtU
```

⚠️ **Note**:
That signature (`l62X...`) is only valid if you sign with a secret like `"super-secret-key"`.
When testing, replace `"super-secret-key"` with your actual `SUPABASE_JWT_SECRET`.

---

# 🗄️ Minimal Supabase User Table Structure

Supabase already has a built-in `auth.users` table, but if you want your **own user metadata table** to match with JWTs, you can keep it minimal:

```sql
create table public.user_profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  email text unique not null,
  plan text not null default 'free', -- 'free', 'pro', 'enterprise'
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Optional trigger to auto-update updated_at
create trigger update_user_profiles_updated_at
before update on public.user_profiles
for each row
execute procedure update_updated_at_column();
```

### Example Row

```sql
insert into public.user_profiles (id, email, plan)
values (
  'c6f6f8b0-1fcd-4a24-bf4a-12d4a4e2f2d8',
  '<EMAIL>',
  'pro'
);
```

---

# 🔄 How n8n Auth Workflow Uses This

1. **Gateway Workflow** extracts `Authorization: Bearer <JWT>` from the request.
2. Calls **Auth Workflow**:

   * Verify JWT with `SUPABASE_JWT_SECRET`.
   * Decode payload → get `sub` (UUID), `email`, and `plan`.
   * Optionally check `user_profiles` table via Supabase REST or Postgres node to confirm plan.
3. Return `{ user, plan }` to Gateway.
4. Gateway routes to the correct worker workflow.

---

👉 Do you want me to also create a **ready-to-run n8n Auth Workflow update** that verifies the JWT against your `SUPABASE_JWT_SECRET` and fetches the plan from the `user_profiles` table automatically?















