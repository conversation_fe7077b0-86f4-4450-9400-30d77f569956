// N8N Code Node for Package Assignment

const SUPABASE_URL = "https://db.homelynx.qzz.io";
const SUPABASE_SERVICE_KEY = "eyJ..."; // Your service role key

// Function to assign package to user
async function assignPackageToUser(userEmail, packageSlug, durationMonths = 1) {
  const response = await fetch(`${SUPABASE_URL}/rpc/upgrade_user_package`, {
    method: 'POST',
    headers: {
      'apikey': SUPABASE_SERVICE_KEY,
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      target_user_email: userEmail,
      new_package_slug: packageSlug,
      subscription_duration_months: durationMonths
    })
  });
  
  if (!response.ok) {
    throw new Error(`Failed to assign package: ${await response.text()}`);
  }
  
  return await response.json();
}

// Function to get user's current package
async function getUserPackage(userEmail) {
  const response = await fetch(
    `${SUPABASE_URL}/rest/v1/user_profiles?select=*,packages(*)&email=eq.${encodeURIComponent(userEmail)}`,
    {
      method: 'GET',
      headers: {
        'apikey': SUPABASE_SERVICE_KEY,
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'Content-Type': 'application/json'
      }
    }
  );
  
  if (!response.ok) {
    throw new Error(`Failed to get user package: ${await response.text()}`);
  }
  
  const data = await response.json();
  return data.length > 0 ? data[0] : null;
}

// Function to list all available packages
async function getAvailablePackages() {
  const response = await fetch(`${SUPABASE_URL}/rest/v1/packages?is_active=eq.true&order=id`, {
    method: 'GET',
    headers: {
      'apikey': SUPABASE_SERVICE_KEY,
      'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get packages: ${await response.text()}`);
  }
  
  return await response.json();
}

// Main execution
const operation = items[0].json.operation || 'assign'; // 'assign', 'get', 'list'
const userEmail = items[0].json.user_email;
const packageSlug = items[0].json.package_slug;
const durationMonths = items[0].json.duration_months || 1;

try {
  switch (operation) {
    case 'assign':
      if (!userEmail || !packageSlug) {
        throw new Error('user_email and package_slug are required for assign operation');
      }
      
      const assignResult = await assignPackageToUser(userEmail, packageSlug, durationMonths);
      
      return [{
        json: {
          success: true,
          operation: 'assign',
          result: assignResult,
          message: `Successfully assigned ${packageSlug} package to ${userEmail}`
        }
      }];
      
    case 'get':
      if (!userEmail) {
        throw new Error('user_email is required for get operation');
      }
      
      const userPackage = await getUserPackage(userEmail);
      
      return [{
        json: {
          success: true,
          operation: 'get',
          user_email: userEmail,
          current_package: userPackage,
          message: userPackage ? `User has ${userPackage.packages.name} package` : 'User not found'
        }
      }];
      
    case 'list':
      const packages = await getAvailablePackages();
      
      return [{
        json: {
          success: true,
          operation: 'list',
          available_packages: packages,
          message: `Found ${packages.length} available packages`
        }
      }];
      
    default:
      throw new Error('Invalid operation. Use: assign, get, or list');
  }
  
} catch (error) {
  return [{
    json: {
      success: false,
      error: error.message,
      operation: operation,
      timestamp: new Date().toISOString()
    }
  }];
}
