{"meta": {"instanceId": "bundle-demo-001"}, "workflows": [{"id": "1", "name": "Auth Workflow", "nodes": [{"parameters": {"path": "auth-check", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Auth Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "const authHeader = $json.headers?.authorization;\nif (!authHeader || !authHeader.startsWith('Bearer ')) {\n  return [{ status: 401, error: 'Missing or invalid Authorization header' }];\n}\n\nconst token = authHeader.split(' ')[1];\n\n// Demo decoding (replace with real JWT verification)\nlet user;\nif (token === 'free-token') user = { email: '<EMAIL>', plan: 'free' };\nelse if (token === 'pro-token') user = { email: '<EMAIL>', plan: 'pro' };\nelse if (token === 'admin-token') user = { email: '<EMAIL>', plan: 'enterprise' };\nelse return [{ status: 403, error: 'Invalid token' }];\n\nreturn [{ status: 200, user, plan: user.plan }];"}, "id": "2", "name": "Validate Token", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Auth Entry)": {"main": [[{"node": "Validate Token", "type": "main", "index": 0}]]}}, "active": false}, {"id": "2", "name": "Gateway Workflow", "nodes": [{"parameters": {"path": "api/:endpoint", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Gateway Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "const endpoint = $json.endpoint;\nconst headers = $json.headers;\n\nreturn [{ endpoint, headers }];"}, "id": "2", "name": "Extract Request", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [480, 300]}, {"parameters": {"authentication": "none", "url": "http://localhost:5678/webhook/auth-check", "options": {}}, "id": "3", "name": "Call Auth Workflow", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [720, 300]}, {"parameters": {"functionCode": "const auth = items[0].json;\nif (auth.status !== 200) {\n  return [{ status: auth.status, error: auth.error }];\n}\n\nconst endpoint = $json.endpoint;\n\n// Access rules\nconst routes = {\n  worker1: { workflow: 'Pro Worker Workflow', allowedPlans: ['pro', 'enterprise'] },\n  worker2: { workflow: 'Free Worker Workflow', allowedPlans: ['free','pro','enterprise'] },\n  adminOnly: { workflow: 'Admin Workflow', allowedPlans: ['enterprise'] }\n};\n\nconst route = routes[endpoint];\nif (!route) return [{ status: 404, error: 'Unknown endpoint' }];\n\nif (!route.allowedPlans.includes(auth.plan)) {\n  return [{ status: 403, error: 'Forbidden: plan not allowed' }];\n}\n\nreturn [{\n  status: 200,\n  nextWorkflow: route.workflow,\n  user: auth.user,\n  plan: auth.plan,\n  endpoint\n}];"}, "id": "4", "name": "Router", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [960, 300]}, {"parameters": {"workflow": "={{$json[\"nextWorkflow\"]}}", "options": {}}, "id": "5", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1200, 300]}], "connections": {"Webhook (Gateway Entry)": {"main": [[{"node": "Extract Request", "type": "main", "index": 0}]]}, "Extract Request": {"main": [[{"node": "Call Auth Workflow", "type": "main", "index": 0}]]}, "Call Auth Workflow": {"main": [[{"node": "Router", "type": "main", "index": 0}]]}, "Router": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}}, "active": false}, {"id": "3", "name": "Free Worker Workflow", "nodes": [{"parameters": {"path": "free-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Free Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello ${$json.user?.email || 'Guest'}!`,\n  plan: $json.plan,\n  note: \"This is a free-tier endpoint available to all plans.\"\n}];"}, "id": "2", "name": "Free Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Free Entry)": {"main": [[{"node": "Free Logic", "type": "main", "index": 0}]]}}, "active": false}, {"id": "4", "name": "Pro Worker Workflow", "nodes": [{"parameters": {"path": "pro-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Pro Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello Pro user ${$json.user?.email || ''}!`,\n  plan: $json.plan,\n  note: \"This is a Pro feature only endpoint.\"\n}];"}, "id": "2", "name": "Pro Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Pro Entry)": {"main": [[{"node": "Pro Logic", "type": "main", "index": 0}]]}}, "active": false}, {"id": "5", "name": "Admin Workflow", "nodes": [{"parameters": {"path": "admin-test", "responseMode": "lastNode", "options": {}}, "id": "1", "name": "Webhook (Admin Entry)", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"functionCode": "return [{\n  status: 200,\n  message: `Hello Admin ${$json.user?.email || ''}!`,\n  user: $json.user,\n  plan: $json.plan,\n  note: \"This is a restricted enterprise-only endpoint.\"\n}];"}, "id": "2", "name": "Admin Logic", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300]}], "connections": {"Webhook (Admin Entry)": {"main": [[{"node": "Admin Logic", "type": "main", "index": 0}]]}}, "active": false}]}