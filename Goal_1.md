Goal: let users sign-up / sign-in **via Supabase**, then let **n8n** call Supabase (and any other internal services) with a proper JWT instead of basic secrets.

We’ll do this in three bite-sized steps:

1. **Expose Supabase Auth endpoints** so n8n can reach them.  
2. **Create a Supabase “application user” table** (because your existing tables are already in Supabase).  
3. **Wire n8n** to (a) call Supabase Auth to log users in and (b) attach the returned JWT on every downstream request.

---

### Step 0 – quick sanity check
- Supabase Studio is reachable at `https://supabase.yourdomain.com` (or the port-mapped URL).  
- Inside Coolify each container sees the others by service name (`http://supabase:54321`, `http://n8n:5678`, etc.).  
If those are not true yet, fix DNS / port mappings first.

---

### Step 1 – enable Supabase Auth & expose it to n8n
1. In **Supabase Studio** → **Authentication** → **Providers**  
   - Enable “Email” provider (or whichever you want).  
   - (Optional) add Google / GitHub OAuth if you need SSO.

2. **Grab the public keys n8n will need**  
   - Settings → API → copy  
     - `SUPABASE_URL`  = `https://supabase.yourdomain.com`  
     - `SUPABASE_ANON_KEY` (you’ll put this in n8n)

---

### Step 2 – create an `app_users` table (or rename yours)
If your existing tables already have a `users` table, just add Supabase Auth’s UUID as a foreign key.

```sql
-- In Supabase SQL editor
CREATE TABLE public.app_users (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name text,
  created_at timestamptz DEFAULT now()
);
```

Then create a Postgres trigger so every new Supabase Auth user automatically gets a row in `app_users`:

```sql
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.app_users(id, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

---

### Step 3 – n8n flow skeleton

A. **HTTP Request node** → **Sign-up / Sign-in**

```
Method: POST
URL: {{$env.SUPABASE_URL}}/auth/v1/signin
Headers:
  apikey: {{$env.SUPABASE_ANON_KEY}}
  Content-Type: application/json
Body:
{
  "email": "{{$json.email}}",
  "password": "{{$json.password}}"
}
```
Store the returned `access_token` in an n8n credential called `supabase_jwt`.

B. **All other Supabase calls**  
Add an **HTTP Request** node that attaches  
```
Authorization: Bearer {{$credentials.supabase_jwt}}
```
and points to `{{$env.SUPABASE_URL}}/rest/v1/<your_table>`.

---

### Next 5 minutes checklist
1. ✅ Confirm n8n can reach `http://supabase:54321` (or external URL).  
2. ✅ Create the `app_users` table + trigger.  
3. ✅ Add the two env vars (`SUPABASE_URL`, `SUPABASE_ANON_KEY`) to the n8n container in Coolify.  
4. ✅ Build the first n8n workflow: “User login” → “Get their profile from `app_users`”.

Reply when step 1 or 2 is done (or if you hit any connection error) and we’ll keep going.
